# 山竹阅卷系统重构快速开始指南

## 立即开始

### 1. 创建重构分支

```bash
# 确保在主分支上
git checkout main
git pull origin main

# 创建并切换到重构分支
git checkout -b refactor/vue-and-chromedp
```

### 2. 备份当前配置

```bash
# 备份当前的前端配置
cp frontend/package.json frontend/package.json.backup
cp frontend/vite.config.ts frontend/vite.config.ts.backup
cp frontend/tsconfig.json frontend/tsconfig.json.backup

# 备份当前的 Go 模块配置
cp go.mod go.mod.backup
cp go.sum go.sum.backup
```

## 第一步：更新前端依赖

### 1. 更新 package.json

```bash
cd frontend
```

将 `package.json` 替换为：

```json
{
  "name": "frontend",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.3.8",
    "@vue/runtime-core": "^3.3.8",
    "element-plus": "^2.4.2",
    "@element-plus/icons-vue": "^2.1.0",
    "pinia": "^2.1.7",
    "vue-router": "^4.2.5"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/tsconfig": "^0.4.0",
    "typescript": "^5.2.2",
    "vite": "^5.0.0",
    "vue-tsc": "^1.8.22",
    "@types/node": "^20.9.0"
  }
}
```

### 2. 更新 vite.config.ts

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-core': ['vue', 'vue-router', 'pinia'],
          'element-plus': ['element-plus', '@element-plus/icons-vue']
        }
      }
    },
    chunkSizeWarningLimit: 500
  }
})
```

### 3. 更新 tsconfig.json

```json
{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["vite/client"]
  }
}
```

### 4. 安装新依赖

```bash
# 删除旧的依赖
rm -rf node_modules package-lock.json

# 安装新依赖
npm install
```

## 第二步：更新后端依赖

### 1. 更新 go.mod

在项目根目录执行：

```bash
# 添加 ChromeDP 依赖
go get github.com/chromedp/chromedp@latest
go get github.com/chromedp/cdproto@latest

# 移除 Playwright 依赖
go mod edit -droprequire github.com/playwright-community/playwright-go
```

### 2. 清理依赖

```bash
go mod tidy
```

## 第三步：创建新的前端结构

### 1. 创建目录结构

```bash
cd frontend/src

# 创建新的目录结构
mkdir -p {components/{common,business,layout},views/{login,home,grading},stores,composables,types,utils,router}

# 移动现有文件到备份目录
mkdir -p ../backup
mv components ../backup/
mv pages ../backup/
mv contexts ../backup/
```

### 2. 创建基础文件

创建 `src/main.ts`：

```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

const app = createApp(App)
const pinia = createPinia()

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

app.mount('#app')
```

创建 `src/App.vue`：

```vue
<template>
  <router-view />
</template>

<script setup lang="ts">
// 应用入口组件
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
```

创建 `src/router/index.ts`：

```typescript
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/LoginView.vue')
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

## 第四步：创建后端模块结构

### 1. 创建目录结构

```bash
# 在项目根目录
mkdir -p internal/{app,browser,grading,config,database,logger}
mkdir -p pkg/{types,utils,errors}
```

### 2. 创建 ChromeDP 浏览器管理器

创建 `internal/browser/manager.go`：

```go
package browser

import (
    "context"
    "time"
    
    "github.com/chromedp/chromedp"
)

type Manager struct {
    allocCtx context.Context
    taskCtx  context.Context
    cancel   context.CancelFunc
}

func NewManager() *Manager {
    return &Manager{}
}

func (m *Manager) Start() error {
    opts := append(chromedp.DefaultExecAllocatorOptions[:],
        chromedp.Flag("headless", false),
        chromedp.Flag("disable-gpu", false),
        chromedp.Flag("disable-dev-shm-usage", true),
        chromedp.Flag("no-sandbox", true),
    )
    
    m.allocCtx, m.cancel = chromedp.NewExecAllocator(context.Background(), opts...)
    m.taskCtx, _ = chromedp.NewContext(m.allocCtx)
    
    return chromedp.Run(m.taskCtx)
}

func (m *Manager) Stop() error {
    if m.cancel != nil {
        m.cancel()
    }
    return nil
}

func (m *Manager) Navigate(url string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.Navigate(url),
        chromedp.WaitReady("body"),
    )
}

func (m *Manager) Screenshot(selector string) ([]byte, error) {
    var buf []byte
    err := chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Screenshot(selector, &buf, chromedp.NodeVisible),
    )
    return buf, err
}

func (m *Manager) Click(selector string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Click(selector),
    )
}

func (m *Manager) Fill(selector, value string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Clear(selector),
        chromedp.SendKeys(selector, value),
    )
}
```

## 第五步：验证环境

### 1. 测试前端构建

```bash
cd frontend
npm run build
```

### 2. 测试后端编译

```bash
# 在项目根目录
go build -o test-build .
```

### 3. 运行开发环境

```bash
# 启动 Wails 开发环境
wails dev
```

## 下一步

环境搭建完成后，按照任务列表继续进行：

1. ✅ 创建重构分支
2. ✅ 更新前端依赖  
3. ✅ 配置 Vite 和 TypeScript
4. ✅ 重构后端项目结构
5. ✅ 实现 ChromeDP 浏览器管理器

接下来需要：
- 迁移浏览器自动化功能
- 重构前端页面组件
- 实现状态管理
- 测试核心功能

## 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 清理缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Go 模块问题**
   ```bash
   # 重新初始化模块
   go mod tidy
   go mod download
   ```

3. **Wails 构建失败**
   ```bash
   # 重新生成绑定
   wails generate module
   ```

需要帮助时，请参考详细的《重构设计和实现流程说明书.md》。
