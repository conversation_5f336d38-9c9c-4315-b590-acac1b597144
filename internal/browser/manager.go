package browser

import (
	"context"
	"fmt"
	"time"

	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
)

// Manager 浏览器管理器
type Manager struct {
	allocCtx context.Context
	taskCtx  context.Context
	cancel   context.CancelFunc
}

// NewManager 创建新的浏览器管理器
func NewManager() *Manager {
	return &Manager{}
}

// Start 启动浏览器
func (m *Manager) Start() error {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false),
		chromedp.Flag("disable-gpu", false),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-web-security", true),
	)

	m.allocCtx, m.cancel = chromedp.NewExecAllocator(context.Background(), opts...)
	m.taskCtx, _ = chromedp.NewContext(m.allocCtx)

	return chromedp.Run(m.taskCtx)
}

// Stop 停止浏览器
func (m *Manager) Stop() error {
	if m.cancel != nil {
		m.cancel()
	}
	return nil
}

// Navigate 导航到指定URL
func (m *Manager) Navigate(url string) error {
	return chromedp.Run(m.taskCtx,
		chromedp.Navigate(url),
		chromedp.WaitReady("body"),
	)
}

// Screenshot 截取指定选择器的元素截图
func (m *Manager) Screenshot(selector string) ([]byte, error) {
	var buf []byte
	err := chromedp.Run(m.taskCtx,
		chromedp.WaitVisible(selector),
		chromedp.Screenshot(selector, &buf, chromedp.NodeVisible),
	)
	return buf, err
}

// ScreenshotArea 截取指定区域的截图
func (m *Manager) ScreenshotArea(x, y, width, height int) ([]byte, error) {
	var buf []byte
	err := chromedp.Run(m.taskCtx,
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 使用page.CaptureScreenshot进行区域截图
			clip := &page.Viewport{
				X:      float64(x),
				Y:      float64(y),
				Width:  float64(width),
				Height: float64(height),
				Scale:  1.0,
			}
			screenshot, err := page.CaptureScreenshot().
				WithClip(clip).
				WithFormat(page.CaptureScreenshotFormatPng).
				Do(ctx)
			if err != nil {
				return err
			}

			buf = screenshot
			return nil
		}),
	)
	if err != nil {
		return nil, fmt.Errorf("capture region screenshot failed: %v", err)
	}
	return buf, nil
}

// Click 点击指定选择器的元素
func (m *Manager) Click(selector string) error {
	return chromedp.Run(m.taskCtx,
		chromedp.WaitVisible(selector),
		chromedp.Click(selector),
	)
}

// Fill 填充指定选择器的元素
func (m *Manager) Fill(selector, value string) error {
	return chromedp.Run(m.taskCtx,
		chromedp.WaitVisible(selector),
		chromedp.Clear(selector),
		chromedp.SendKeys(selector, value),
	)
}

// WaitForElement 等待元素出现
func (m *Manager) WaitForElement(selector string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(m.taskCtx, timeout)
	defer cancel()

	return chromedp.Run(ctx,
		chromedp.WaitVisible(selector),
	)
}

// GetTextContent 获取元素文本内容
func (m *Manager) GetTextContent(selector string) (string, error) {
	var text string
	err := chromedp.Run(m.taskCtx,
		chromedp.WaitVisible(selector),
		chromedp.Text(selector, &text),
	)
	return text, err
}

// IsElementVisible 检查元素是否可见
func (m *Manager) IsElementVisible(selector string) (bool, error) {
	var visible bool
	err := chromedp.Run(m.taskCtx,
		chromedp.Evaluate(fmt.Sprintf(`
			(() => {
				const element = document.querySelector('%s');
				return element && element.offsetParent !== null;
			})()
		`, selector), &visible),
	)
	return visible, err
}
