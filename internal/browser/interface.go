package browser

import (
	"time"
)

// BrowserOperator 浏览器操作接口
type BrowserOperator interface {
	// 生命周期管理
	Start() error
	Stop() error

	// 页面导航
	Navigate(url string) error

	// 截图功能
	Screenshot(selector string) ([]byte, error)
	ScreenshotArea(x, y, width, height int) ([]byte, error)

	// 元素操作
	Click(selector string) error
	Fill(selector, value string) error
	GetTextContent(selector string) (string, error)
	IsElementVisible(selector string) (bool, error)

	// 等待操作
	WaitForElement(selector string, timeout time.Duration) error
}

// 确保 Manager 实现了 BrowserOperator 接口
var _ BrowserOperator = (*Manager)(nil)
