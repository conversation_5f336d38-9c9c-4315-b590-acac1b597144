package app

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ai-grading/internal/api"
	"ai-grading/internal/browser"
	"ai-grading/internal/config"
	"ai-grading/pkg/utils"
)

// App 应用核心结构
type App struct {
	ctx                  context.Context
	browserManager       browser.BrowserOperator
	apiClient            *api.Client
	configManager        *config.Manager
	loading              bool
	autoGrading          bool
	autoGradingDone      chan bool
	autoGradingCtx       context.Context
	autoGradingCancel    context.CancelFunc
	apiRequestInProgress bool
	apiRequestMutex      sync.Mutex
	recentScreenshots    []string
	sameScreenshotCount  int
}

// NewApp 创建新的应用实例
func NewApp() *App {
	return &App{
		autoGrading:         false,
		autoGradingDone:     make(chan bool, 1),
		recentScreenshots:   make([]string, 0, 3),
		sameScreenshotCount: 0,
		browserManager:      browser.NewManager(),
	}
}

// Startup 应用启动
func (a *App) Startup(ctx context.Context) {
	a.ctx = ctx

	// 初始化配置管理器
	configDir, err := config.GetConfigDir()
	if err != nil {
		fmt.Printf("Failed to get config directory: %v\n", err)
		return
	}

	a.configManager = config.NewManager(configDir)

	// 加载配置
	cfg, err := a.configManager.Load()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}

	// 初始化API客户端
	apiClient, err := api.NewClient(ctx, cfg.API.BaseURL)
	if err != nil {
		fmt.Printf("Failed to initialize API client: %v\n", err)
		// 不阻止应用启动，但记录错误
	} else {
		a.apiClient = apiClient
		fmt.Printf("API client initialized successfully with base URL: %s\n", cfg.API.BaseURL)
	}
}

// Shutdown 应用关闭
func (a *App) Shutdown(ctx context.Context) {
	if a.browserManager != nil {
		a.browserManager.Stop()
	}

	// 清理API客户端资源
	if a.apiClient != nil {
		// API客户端的清理逻辑（如果需要）
	}
}

// GetVersion 获取版本信息
func (a *App) GetVersion() string {
	return "v0.1.0"
}

// StartLoading 开始加载
func (a *App) StartLoading() {
	a.loading = true
}

// StopLoading 停止加载
func (a *App) StopLoading() {
	a.loading = false
}

// IsLoading 检查是否正在加载
func (a *App) IsLoading() bool {
	return a.loading
}

// OpenURL 打开指定URL
func (a *App) OpenURL(urlName string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	// 启动浏览器
	if err := a.browserManager.Start(); err != nil {
		return fmt.Errorf("failed to start browser: %v", err)
	}

	var url string
	if urlName == "custom" {
		url = "about:blank"
	} else {
		// TODO: 从配置中获取URL
		url = "https://example.com"
	}

	// 导航到URL
	if err := a.browserManager.Navigate(url); err != nil {
		return fmt.Errorf("failed to navigate to URL: %v", err)
	}

	return nil
}

// ScreenshotElement 截取元素截图
func (a *App) ScreenshotElement(selector string, maxWidth int) (string, error) {
	if a.browserManager == nil {
		return "", fmt.Errorf("browser manager not initialized")
	}

	screenshotBytes, err := a.browserManager.Screenshot(selector)
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}

	// 处理截图数据并转换为base64
	return utils.ProcessScreenshot(screenshotBytes, maxWidth)
}

// ScreenshotArea 截取区域截图
func (a *App) ScreenshotArea(x, y, width, height, maxWidth int) (string, error) {
	if a.browserManager == nil {
		return "", fmt.Errorf("browser manager not initialized")
	}

	screenshotBytes, err := a.browserManager.ScreenshotArea(x, y, width, height)
	if err != nil {
		return "", fmt.Errorf("failed to take area screenshot: %v", err)
	}

	// 处理截图数据并转换为base64
	return utils.ProcessScreenshot(screenshotBytes, maxWidth)
}

// ClickElement 点击元素
func (a *App) ClickElement(selector string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.Click(selector)
}

// FillElement 填充元素
func (a *App) FillElement(selector, value string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.Fill(selector, value)
}

// ExecuteElementOperations 执行元素操作序列
func (a *App) ExecuteElementOperations(operationsJSON string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	// TODO: 解析JSON并执行操作序列
	// 这里需要解析operationsJSON并按顺序执行操作
	// 暂时返回nil，后续实现
	return nil
}

// FindLoginButton 查找登录按钮
func (a *App) FindLoginButton() (bool, error) {
	if a.browserManager == nil {
		return false, fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.IsElementVisible("button:has-text('登录')")
}

// ============= API 相关方法 =============

// APILogin 用户登录
func (a *App) APILogin(email, password string) (*api.LoginResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.Login(email, password)
}

// APIEmailLogin 邮箱验证码登录
func (a *App) APIEmailLogin(email, code string) (*api.LoginResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.EmailLogin(email, code)
}

// APISendEmailCode 发送邮箱验证码
func (a *App) APISendEmailCode(email string) (*api.EmailCodeResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.SendEmailCode(email)
}

// APIRegister 用户注册
func (a *App) APIRegister(email, password string) (*api.RegisterResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.Register(email, password)
}

// APIRecover 密码恢复
func (a *App) APIRecover(email string) (*api.RecoverResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.Recover(email)
}

// APIUpdatePassword 更新密码
func (a *App) APIUpdatePassword(password string) (*api.UpdatePasswordResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.UpdatePassword(password)
}

// APIVerify 验证令牌
func (a *App) APIVerify(email, token, tokenType string) (*api.VerifyResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.Verify(email, token, tokenType)
}

// APILogout 登出
func (a *App) APILogout() error {
	if a.apiClient == nil {
		return fmt.Errorf("API client not initialized")
	}

	return a.apiClient.Logout()
}

// APIRefreshToken 刷新访问令牌
func (a *App) APIRefreshToken() (*api.LoginResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.RefreshToken()
}

// APIGetAppVersion 获取应用版本信息
func (a *App) APIGetAppVersion() (*api.AppVersionConfig, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.GetAppVersion()
}

// APIGetWalletBalance 查询钱包余额
func (a *App) APIGetWalletBalance() (*api.WalletBalance, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.GetWalletBalance()
}

// APISubmitRechargeRequest 提交充值申请
func (a *App) APISubmitRechargeRequest(amount float64, paymentMethod string) (*api.RechargeResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.SubmitRechargeRequest(amount, paymentMethod)
}

// APIGetTOSCredentials 获取TOS临时凭证
func (a *App) APIGetTOSCredentials() (*api.TOSCredentials, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.GetTOSCredentials()
}

// APIAnalyzeImage AI图像分析
func (a *App) APIAnalyzeImage(image, criteria, subject, analysisMode string) (*api.AnalysisResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.AnalyzeImage(image, criteria, subject, analysisMode)
}

// APIGetRechargeList 查询充值申请列表
func (a *App) APIGetRechargeList() ([]api.RechargeResponse, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.GetRechargeList()
}

// APIGetTransactions 查询交易记录
func (a *App) APIGetTransactions() ([]map[string]any, error) {
	if a.apiClient == nil {
		return nil, fmt.Errorf("API client not initialized")
	}

	return a.apiClient.GetTransactions()
}

// APIIsAuthenticated 检查认证状态
func (a *App) APIIsAuthenticated() bool {
	if a.apiClient == nil {
		return false
	}

	return a.apiClient.IsAuthenticated()
}

// APIGetAccessToken 获取访问令牌
func (a *App) APIGetAccessToken() string {
	if a.apiClient == nil {
		return ""
	}

	return a.apiClient.GetAccessToken()
}

// APICheckConnection 检查API连接
func (a *App) APICheckConnection() bool {
	if a.apiClient == nil {
		return false
	}

	return a.apiClient.CheckConnection()
}

// APIHealthCheck 健康检查
func (a *App) APIHealthCheck() map[string]any {
	if a.apiClient == nil {
		return map[string]any{
			"status": "error",
			"error":  "API client not initialized",
		}
	}

	return a.apiClient.HealthCheck()
}

// APIGetAuthStatus 获取认证状态详情
func (a *App) APIGetAuthStatus() map[string]any {
	if a.apiClient == nil {
		return map[string]any{
			"isAuthenticated": false,
			"hasValidToken":   false,
			"shouldRefresh":   false,
			"error":           "API client not initialized",
		}
	}

	return a.apiClient.GetAuthStatus()
}

// APIUpdateConfig 更新API配置
func (a *App) APIUpdateConfig(baseURL string, timeoutSeconds int) error {
	if a.apiClient == nil {
		return fmt.Errorf("API client not initialized")
	}

	// 更新配置文件
	if a.configManager != nil {
		apiConfig := config.APIConfig{
			BaseURL:        baseURL,
			TimeoutSeconds: timeoutSeconds,
			Retries:        3,
			RetryDelay:     1000,
		}

		if err := a.configManager.UpdateAPI(apiConfig); err != nil {
			return fmt.Errorf("update config file: %w", err)
		}
	}

	// 更新API客户端配置
	timeout := time.Duration(timeoutSeconds) * time.Second
	a.apiClient.UpdateConfig(baseURL, timeout)
	return nil
}

// ============= 配置管理方法 =============

// GetConfig 获取应用配置
func (a *App) GetConfig() (*config.Config, error) {
	if a.configManager == nil {
		return nil, fmt.Errorf("config manager not initialized")
	}

	return a.configManager.Load()
}

// UpdateAPIConfig 更新API配置
func (a *App) UpdateAPIConfig(apiConfig config.APIConfig) error {
	if a.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	if err := a.configManager.UpdateAPI(apiConfig); err != nil {
		return fmt.Errorf("update API config: %w", err)
	}

	// 同时更新API客户端配置
	if a.apiClient != nil {
		timeout := time.Duration(apiConfig.TimeoutSeconds) * time.Second
		a.apiClient.UpdateConfig(apiConfig.BaseURL, timeout)
	}

	return nil
}

// UpdateAppConfig 更新应用配置
func (a *App) UpdateAppConfig(appConfig config.AppConfig) error {
	if a.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	return a.configManager.UpdateApp(appConfig)
}

// UpdateBrowserConfig 更新浏览器配置
func (a *App) UpdateBrowserConfig(browserConfig config.BrowserConfig) error {
	if a.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	return a.configManager.UpdateBrowser(browserConfig)
}

// UpdateGradingConfig 更新阅卷配置
func (a *App) UpdateGradingConfig(gradingConfig config.GradingConfig) error {
	if a.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	return a.configManager.UpdateGrading(gradingConfig)
}

// GetConfigDirs 获取配置目录信息
func (a *App) GetConfigDirs() (map[string]string, error) {
	configDir, err := config.GetConfigDir()
	if err != nil {
		return nil, fmt.Errorf("get config dir: %w", err)
	}

	dataDir, err := config.GetDataDir()
	if err != nil {
		return nil, fmt.Errorf("get data dir: %w", err)
	}

	logsDir, err := config.GetLogsDir()
	if err != nil {
		return nil, fmt.Errorf("get logs dir: %w", err)
	}

	cacheDir, err := config.GetCacheDir()
	if err != nil {
		return nil, fmt.Errorf("get cache dir: %w", err)
	}

	return map[string]string{
		"config": configDir,
		"data":   dataDir,
		"logs":   logsDir,
		"cache":  cacheDir,
	}, nil
}

// StartAutoGrading 开始自动阅卷
func (a *App) StartAutoGrading(paperCount int) error {
	if a.autoGrading {
		return fmt.Errorf("auto grading is already running")
	}

	a.autoGrading = true
	a.autoGradingCtx, a.autoGradingCancel = context.WithCancel(a.ctx)

	// TODO: 实现自动阅卷逻辑
	go func() {
		defer func() {
			a.autoGrading = false
		}()

		// 自动阅卷逻辑将在后续实现
	}()

	return nil
}

// StopAutoGrading 停止自动阅卷
func (a *App) StopAutoGrading() error {
	if !a.autoGrading {
		return fmt.Errorf("auto grading is not running")
	}

	if a.autoGradingCancel != nil {
		a.autoGradingCancel()
	}

	a.autoGrading = false
	return nil
}

// IsAutoGrading 检查是否正在自动阅卷
func (a *App) IsAutoGrading() bool {
	return a.autoGrading
}
