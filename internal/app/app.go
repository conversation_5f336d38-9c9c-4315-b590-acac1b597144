package app

import (
	"context"
	"fmt"
	"sync"

	"ai-grading/internal/browser"
	"ai-grading/pkg/utils"
)

// App 应用核心结构
type App struct {
	ctx                  context.Context
	browserManager       browser.BrowserOperator
	loading              bool
	autoGrading          bool
	autoGradingDone      chan bool
	autoGradingCtx       context.Context
	autoGradingCancel    context.CancelFunc
	apiRequestInProgress bool
	apiRequestMutex      sync.Mutex
	recentScreenshots    []string
	sameScreenshotCount  int
}

// NewApp 创建新的应用实例
func NewApp() *App {
	return &App{
		autoGrading:         false,
		autoGradingDone:     make(chan bool, 1),
		recentScreenshots:   make([]string, 0, 3),
		sameScreenshotCount: 0,
		browserManager:      browser.NewManager(),
	}
}

// Startup 应用启动
func (a *App) Startup(ctx context.Context) {
	a.ctx = ctx
}

// Shutdown 应用关闭
func (a *App) Shutdown(ctx context.Context) {
	if a.browserManager != nil {
		a.browserManager.Stop()
	}
}

// GetVersion 获取版本信息
func (a *App) GetVersion() string {
	return "v0.1.0"
}

// StartLoading 开始加载
func (a *App) StartLoading() {
	a.loading = true
}

// StopLoading 停止加载
func (a *App) StopLoading() {
	a.loading = false
}

// IsLoading 检查是否正在加载
func (a *App) IsLoading() bool {
	return a.loading
}

// OpenURL 打开指定URL
func (a *App) OpenURL(urlName string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	// 启动浏览器
	if err := a.browserManager.Start(); err != nil {
		return fmt.Errorf("failed to start browser: %v", err)
	}

	var url string
	if urlName == "custom" {
		url = "about:blank"
	} else {
		// TODO: 从配置中获取URL
		url = "https://example.com"
	}

	// 导航到URL
	if err := a.browserManager.Navigate(url); err != nil {
		return fmt.Errorf("failed to navigate to URL: %v", err)
	}

	return nil
}

// ScreenshotElement 截取元素截图
func (a *App) ScreenshotElement(selector string, maxWidth int) (string, error) {
	if a.browserManager == nil {
		return "", fmt.Errorf("browser manager not initialized")
	}

	screenshotBytes, err := a.browserManager.Screenshot(selector)
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}

	// 处理截图数据并转换为base64
	return utils.ProcessScreenshot(screenshotBytes, maxWidth)
}

// ScreenshotArea 截取区域截图
func (a *App) ScreenshotArea(x, y, width, height, maxWidth int) (string, error) {
	if a.browserManager == nil {
		return "", fmt.Errorf("browser manager not initialized")
	}

	screenshotBytes, err := a.browserManager.ScreenshotArea(x, y, width, height)
	if err != nil {
		return "", fmt.Errorf("failed to take area screenshot: %v", err)
	}

	// 处理截图数据并转换为base64
	return utils.ProcessScreenshot(screenshotBytes, maxWidth)
}

// ClickElement 点击元素
func (a *App) ClickElement(selector string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.Click(selector)
}

// FillElement 填充元素
func (a *App) FillElement(selector, value string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.Fill(selector, value)
}

// ExecuteElementOperations 执行元素操作序列
func (a *App) ExecuteElementOperations(operationsJSON string) error {
	if a.browserManager == nil {
		return fmt.Errorf("browser manager not initialized")
	}

	// TODO: 解析JSON并执行操作序列
	// 这里需要解析operationsJSON并按顺序执行操作
	// 暂时返回nil，后续实现
	return nil
}

// FindLoginButton 查找登录按钮
func (a *App) FindLoginButton() (bool, error) {
	if a.browserManager == nil {
		return false, fmt.Errorf("browser manager not initialized")
	}

	return a.browserManager.IsElementVisible("button:has-text('登录')")
}

// StartAutoGrading 开始自动阅卷
func (a *App) StartAutoGrading(paperCount int) error {
	if a.autoGrading {
		return fmt.Errorf("auto grading is already running")
	}

	a.autoGrading = true
	a.autoGradingCtx, a.autoGradingCancel = context.WithCancel(a.ctx)

	// TODO: 实现自动阅卷逻辑
	go func() {
		defer func() {
			a.autoGrading = false
		}()

		// 自动阅卷逻辑将在后续实现
	}()

	return nil
}

// StopAutoGrading 停止自动阅卷
func (a *App) StopAutoGrading() error {
	if !a.autoGrading {
		return fmt.Errorf("auto grading is not running")
	}

	if a.autoGradingCancel != nil {
		a.autoGradingCancel()
	}

	a.autoGrading = false
	return nil
}

// IsAutoGrading 检查是否正在自动阅卷
func (a *App) IsAutoGrading() bool {
	return a.autoGrading
}
