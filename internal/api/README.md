# AI Grading API Service

这是一个高可维护性的 API 调用服务，专为 Wails 应用设计，提供完整的认证和业务接口调用功能。

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────┐
│   App Layer     │  ← Wails 绑定层
├─────────────────┤
│  API Client     │  ← 主客户端
├─────────────────┤
│ Auth | Business │  ← 服务层
├─────────────────┤
│  HTTP Client    │  ← HTTP 基础层
├─────────────────┤
│ Token Manager   │  ← Token 管理层
└─────────────────┘
```

### 核心组件

- **HTTPClient**: HTTP 请求基础类，提供统一的网络请求处理
- **TokenManager**: 智能 Token 管理，自动存储、刷新、过期检查
- **AuthService**: 完整的认证服务，支持多种登录方式
- **BusinessService**: 业务接口封装，统一调用方式
- **Client**: 主客户端，整合所有服务和拦截器

## 📋 支持的接口

### 认证相关 (8个)

1. **发送邮箱验证码** - `SendEmailCode(email)`
2. **邮箱验证码登录** - `EmailLogin(email, code)`
3. **用户登录** - `Login(email, password)`
4. **密码恢复** - `Recover(email)`
5. **刷新访问令牌** - `RefreshToken()`
6. **用户注册** - `Register(email, password)`
7. **更新密码** - `UpdatePassword(password)`
8. **验证令牌** - `Verify(email, token, tokenType)`

### 业务相关 (5个)

1. **提交充值申请** - `SubmitRechargeRequest(amount, paymentMethod)`
2. **获取TOS临时凭证** - `GetTOSCredentials()`
3. **获取应用版本信息** - `GetAppVersion()`
4. **查询钱包余额** - `GetWalletBalance()`
5. **AI图像分析** - `AnalyzeImage(image, criteria, subject, analysisMode)`

## 🚀 核心特性

### 1. 自动 Token 管理

- ✅ 使用 `app` 前缀存储到配置文件
- ✅ 提前 5 分钟自动刷新
- ✅ 刷新失败自动清除并跳转登录
- ✅ 线程安全的并发访问

### 2. 智能错误处理

- ✅ 分类错误类型（ApiError、NetworkError、AuthenticationError）
- ✅ 自动重试机制（网络错误重试，认证错误不重试）
- ✅ 用户友好的错误消息
- ✅ 详细的错误日志记录

### 3. 完整类型安全

- ✅ 基于 OpenAPI 规范的 TypeScript 类型
- ✅ 编译时类型检查
- ✅ 智能代码提示

### 4. 配置管理

- ✅ 统一的配置文件管理
- ✅ 支持运行时配置更新
- ✅ 默认配置自动生成

## 💻 使用方法

### 在 Wails App 中使用

```go
// 在 App 结构中，API 客户端会在 Startup 时自动初始化

// 认证相关
func (a *App) Login(email, password string) (*api.LoginResponse, error) {
    return a.APILogin(email, password)
}

// 业务相关
func (a *App) GetBalance() (*api.WalletBalance, error) {
    return a.APIGetWalletBalance()
}

// 检查认证状态
func (a *App) IsLoggedIn() bool {
    return a.APIIsAuthenticated()
}
```

### 在前端调用

```javascript
// 登录
const response = await window.go.app.App.APILogin("<EMAIL>", "password");

// 获取余额
const balance = await window.go.app.App.APIGetWalletBalance();

// AI 图像分析
const result = await window.go.app.App.APIAnalyzeImage(
    imageBase64, 
    criteria, 
    subject, 
    analysisMode
);

// 检查认证状态
const isAuth = await window.go.app.App.APIIsAuthenticated();
```

## 🔧 配置管理

### 配置文件结构

```json
{
  "api": {
    "base_url": "https://api.example.com",
    "timeout_seconds": 30,
    "retries": 3,
    "retry_delay_ms": 1000
  },
  "app": {
    "version": "1.0.0",
    "environment": "production",
    "log_level": "info",
    "data_dir": ""
  },
  "browser": {
    "headless": false,
    "user_agent": "AI-Grading-Browser/1.0",
    "window_width": 1280,
    "window_height": 720
  },
  "grading": {
    "default_subject": "数学",
    "default_criteria": "请根据标准答案评分",
    "analysis_mode": "standard",
    "max_image_size": 5242880,
    "supported_formats": ["jpg", "jpeg", "png", "webp"]
  }
}
```

### 配置目录

- **配置目录**: `~/.ai-grading/`
- **配置文件**: `~/.ai-grading/config.json`
- **Token 文件**: `~/.ai-grading/app_tokens.json`
- **数据目录**: `~/.ai-grading/data/`
- **日志目录**: `~/.ai-grading/logs/`
- **缓存目录**: `~/.ai-grading/cache/`

## 🧪 测试

运行测试：

```bash
cd internal/api
go test -v
```

测试覆盖：

```bash
go test -v -cover
```

## 🔒 安全特性

### Token 安全

- ✅ Token 文件权限设置为 600（仅用户可读写）
- ✅ 内存中的 Token 通过 mutex 保护
- ✅ 自动清除过期 Token
- ✅ 刷新失败时自动清除所有 Token

### 网络安全

- ✅ HTTPS 强制使用
- ✅ 请求超时保护
- ✅ 重试次数限制
- ✅ 用户代理标识

## 📊 错误处理

### 错误类型

```go
type APIError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details"`
}

type NetworkError struct {
    Operation string
    URL       string
    Err       error
}

type AuthenticationError struct {
    Message string
    Code    int
}

type ValidationError struct {
    Field   string
    Message string
    Code    int
}
```

### 错误处理策略

- **网络错误**: 自动重试（最多3次）
- **认证错误**: 不重试，清除 Token
- **验证错误**: 不重试，返回详细错误信息
- **服务器错误**: 重试一次

## 🔄 自动刷新机制

Token 自动刷新逻辑：

1. 每次 API 调用前检查 Token 是否即将过期（5分钟内）
2. 如果即将过期，自动调用刷新接口
3. 刷新成功后更新本地 Token
4. 刷新失败则清除所有 Token 并要求重新登录

## 📝 日志记录

使用 Wails 运行时日志系统：

```go
runtime.LogInfo(ctx, "API operation successful")
runtime.LogError(ctx, "API operation failed: %v", err)
```

日志级别：
- **Info**: 正常操作记录
- **Error**: 错误和异常记录
- **Debug**: 详细调试信息（开发模式）

## 🚀 性能优化

- ✅ HTTP 连接复用
- ✅ 请求/响应压缩
- ✅ 智能重试机制
- ✅ 并发安全设计
- ✅ 内存高效的 JSON 处理

## 🔮 扩展性

### 添加新接口

1. 在 `types.go` 中定义请求/响应类型
2. 在对应的服务中添加方法
3. 在 `client.go` 中添加便捷方法
4. 在 `app.go` 中添加 Wails 绑定方法

### 添加新服务

1. 创建新的服务文件（如 `admin_service.go`）
2. 在 `client.go` 中集成新服务
3. 添加相应的测试文件

这个 API 服务提供了一个生产就绪的、高可维护性的基础设施，支持自动 Token 管理、智能错误处理、类型安全和开发友好的接口。
