package api

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// TokenManager Token管理器
type TokenManager struct {
	mu               sync.RWMutex
	tokenInfo        *TokenInfo
	configDir        string
	refreshThreshold time.Duration
}

// NewTokenManager 创建新的Token管理器
func NewTokenManager(configDir string) *TokenManager {
	return &TokenManager{
		configDir:        configDir,
		refreshThreshold: 5 * time.Minute, // 提前5分钟刷新
	}
}

// GetAccessToken 获取访问令牌
func (tm *TokenManager) GetAccessToken() string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return ""
	}
	return tm.tokenInfo.AccessToken
}

// GetRefreshToken 获取刷新令牌
func (tm *TokenManager) GetRefreshToken() string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return ""
	}
	return tm.tokenInfo.RefreshToken
}

// SetTokens 设置令牌信息
func (tm *TokenManager) SetTokens(tokenInfo *TokenInfo) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.tokenInfo = tokenInfo
	return tm.saveToFile()
}

// UpdateAccessToken 更新访问令牌（保持刷新令牌不变）
func (tm *TokenManager) UpdateAccessToken(accessToken string, expiresAt int64, expiresIn int) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.tokenInfo == nil {
		return fmt.Errorf("no existing token info")
	}

	tm.tokenInfo.AccessToken = accessToken
	tm.tokenInfo.ExpiresAt = expiresAt
	tm.tokenInfo.ExpiresIn = expiresIn

	return tm.saveToFile()
}

// ClearTokens 清除所有令牌
func (tm *TokenManager) ClearTokens() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.tokenInfo = nil

	// 删除配置文件
	configPath := tm.getConfigPath()
	if _, err := os.Stat(configPath); err == nil {
		return os.Remove(configPath)
	}

	return nil
}

// IsTokenExpired 检查访问令牌是否已过期
func (tm *TokenManager) IsTokenExpired() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return true
	}

	return time.Now().Unix() >= tm.tokenInfo.ExpiresAt
}

// ShouldRefreshToken 检查是否应该刷新令牌
func (tm *TokenManager) ShouldRefreshToken() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return false
	}

	refreshTime := time.Unix(tm.tokenInfo.ExpiresAt, 0).Add(-tm.refreshThreshold)
	return time.Now().After(refreshTime)
}

// HasValidToken 检查是否有有效的令牌
func (tm *TokenManager) HasValidToken() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	return tm.tokenInfo != nil &&
		tm.tokenInfo.AccessToken != "" &&
		!tm.IsTokenExpired()
}

// GetTokenInfo 获取完整的令牌信息
func (tm *TokenManager) GetTokenInfo() *TokenInfo {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return nil
	}

	// 返回副本以避免并发修改
	return &TokenInfo{
		AccessToken:  tm.tokenInfo.AccessToken,
		RefreshToken: tm.tokenInfo.RefreshToken,
		ExpiresAt:    tm.tokenInfo.ExpiresAt,
		ExpiresIn:    tm.tokenInfo.ExpiresIn,
	}
}

// GetRemainingTime 获取令牌剩余有效时间
func (tm *TokenManager) GetRemainingTime() time.Duration {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return 0
	}

	remaining := time.Unix(tm.tokenInfo.ExpiresAt, 0).Sub(time.Now())
	if remaining < 0 {
		return 0
	}

	return remaining
}

// LoadFromFile 从文件加载令牌信息
func (tm *TokenManager) LoadFromFile() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	configPath := tm.getConfigPath()

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil // 文件不存在，不是错误
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("read token file: %w", err)
	}

	var tokenInfo TokenInfo
	if err := json.Unmarshal(data, &tokenInfo); err != nil {
		return fmt.Errorf("unmarshal token info: %w", err)
	}

	tm.tokenInfo = &tokenInfo
	return nil
}

// saveToFile 保存令牌信息到文件
func (tm *TokenManager) saveToFile() error {
	if tm.tokenInfo == nil {
		return nil
	}

	// 确保配置目录存在
	if err := os.MkdirAll(tm.configDir, 0o755); err != nil {
		return fmt.Errorf("create config directory: %w", err)
	}

	data, err := json.MarshalIndent(tm.tokenInfo, "", "  ")
	if err != nil {
		return fmt.Errorf("marshal token info: %w", err)
	}

	configPath := tm.getConfigPath()
	if err := os.WriteFile(configPath, data, 0o600); err != nil {
		return fmt.Errorf("write token file: %w", err)
	}

	return nil
}

// getConfigPath 获取配置文件路径
func (tm *TokenManager) getConfigPath() string {
	return filepath.Join(tm.configDir, "app_tokens.json")
}

// SetTokensFromResponse 从登录响应设置令牌
func (tm *TokenManager) SetTokensFromResponse(response *LoginResponse) error {
	if response.AccessToken == "" || response.RefreshToken == "" {
		return fmt.Errorf("invalid token response")
	}

	tokenInfo := &TokenInfo{
		AccessToken:  response.AccessToken,
		RefreshToken: response.RefreshToken,
		ExpiresAt:    response.ExpiresAt,
		ExpiresIn:    response.ExpiresIn,
	}

	return tm.SetTokens(tokenInfo)
}

// UpdateTokensFromResponse 从刷新响应更新令牌
func (tm *TokenManager) UpdateTokensFromResponse(response *LoginResponse) error {
	if response.AccessToken == "" {
		return fmt.Errorf("invalid refresh response")
	}

	return tm.UpdateAccessToken(response.AccessToken, response.ExpiresAt, response.ExpiresIn)
}

// GetAuthHeaders 获取认证头部
func (tm *TokenManager) GetAuthHeaders() map[string]string {
	token := tm.GetAccessToken()
	if token == "" {
		return nil
	}

	return map[string]string{
		"X-Access-Token": token,
	}
}

// GetRefreshHeaders 获取刷新令牌头部
func (tm *TokenManager) GetRefreshHeaders() map[string]string {
	refreshToken := tm.GetRefreshToken()
	if refreshToken == "" {
		return nil
	}

	return map[string]string{
		"X-Refresh-Token": refreshToken,
	}
}

// LogTokenStatus 记录令牌状态（用于调试）
func (tm *TokenManager) LogTokenStatus(ctx context.Context) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		runtime.LogInfo(ctx, "Token status: No token")
		return
	}

	remaining := tm.GetRemainingTime()
	expired := tm.IsTokenExpired()
	shouldRefresh := tm.ShouldRefreshToken()

	runtime.LogInfof(ctx, "Token status: expired=%v, shouldRefresh=%v, remaining=%v",
		expired, shouldRefresh, remaining)
}
