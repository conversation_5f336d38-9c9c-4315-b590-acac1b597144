package api

import (
	"log/slog"
	"sync"
	"time"
)

// TokenManager Token管理器（内存存储）
type TokenManager struct {
	mu               sync.RWMutex
	tokenInfo        *TokenInfo
	refreshThreshold time.Duration
}

// NewTokenManager 创建新的Token管理器
func NewTokenManager() *TokenManager {
	return &TokenManager{
		refreshThreshold: 5 * time.Minute, // 提前5分钟刷新
	}
}

// GetAccessToken 获取访问令牌
func (tm *TokenManager) GetAccessToken() string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return ""
	}
	return tm.tokenInfo.AccessToken
}

// GetRefreshToken 获取刷新令牌
func (tm *TokenManager) GetRefreshToken() string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return ""
	}
	return tm.tokenInfo.RefreshToken
}

// SetTokens 设置令牌信息
func (tm *TokenManager) SetTokens(tokenInfo *TokenInfo) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.tokenInfo = tokenInfo
	slog.Info("Token set successfully")
}

// UpdateAccessToken 更新访问令牌（保持刷新令牌不变）
func (tm *TokenManager) UpdateAccessToken(accessToken string, expiresAt int64, expiresIn int) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.tokenInfo == nil {
		slog.Warn("No existing token info to update")
		return
	}

	tm.tokenInfo.AccessToken = accessToken
	tm.tokenInfo.ExpiresAt = expiresAt
	tm.tokenInfo.ExpiresIn = expiresIn

	slog.Info("Access token updated successfully")
}

// ClearTokens 清除所有令牌
func (tm *TokenManager) ClearTokens() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.tokenInfo = nil
	slog.Info("Tokens cleared")
}

// IsTokenExpired 检查访问令牌是否已过期
func (tm *TokenManager) IsTokenExpired() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return true
	}

	return time.Now().Unix() >= tm.tokenInfo.ExpiresAt
}

// ShouldRefreshToken 检查是否应该刷新令牌
func (tm *TokenManager) ShouldRefreshToken() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return false
	}

	refreshTime := time.Unix(tm.tokenInfo.ExpiresAt, 0).Add(-tm.refreshThreshold)
	return time.Now().After(refreshTime)
}

// HasValidToken 检查是否有有效的令牌
func (tm *TokenManager) HasValidToken() bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	return tm.tokenInfo != nil &&
		tm.tokenInfo.AccessToken != "" &&
		!tm.IsTokenExpired()
}

// GetTokenInfo 获取完整的令牌信息
func (tm *TokenManager) GetTokenInfo() *TokenInfo {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return nil
	}

	// 返回副本以避免并发修改
	return &TokenInfo{
		AccessToken:  tm.tokenInfo.AccessToken,
		RefreshToken: tm.tokenInfo.RefreshToken,
		ExpiresAt:    tm.tokenInfo.ExpiresAt,
		ExpiresIn:    tm.tokenInfo.ExpiresIn,
	}
}

// GetRemainingTime 获取令牌剩余有效时间
func (tm *TokenManager) GetRemainingTime() time.Duration {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		return 0
	}

	remaining := time.Unix(tm.tokenInfo.ExpiresAt, 0).Sub(time.Now())
	if remaining < 0 {
		return 0
	}

	return remaining
}

// 移除了文件相关的方法，Token 现在只存储在内存中

// SetTokensFromResponse 从登录响应设置令牌
func (tm *TokenManager) SetTokensFromResponse(response *LoginResponse) {
	if response.AccessToken == "" || response.RefreshToken == "" {
		slog.Warn("Invalid token response")
		return
	}

	tokenInfo := &TokenInfo{
		AccessToken:  response.AccessToken,
		RefreshToken: response.RefreshToken,
		ExpiresAt:    response.ExpiresAt,
		ExpiresIn:    response.ExpiresIn,
	}

	tm.SetTokens(tokenInfo)
}

// UpdateTokensFromResponse 从刷新响应更新令牌
func (tm *TokenManager) UpdateTokensFromResponse(response *LoginResponse) {
	if response.AccessToken == "" {
		slog.Warn("Invalid refresh response")
		return
	}

	tm.UpdateAccessToken(response.AccessToken, response.ExpiresAt, response.ExpiresIn)
}

// GetAuthHeaders 获取认证头部
func (tm *TokenManager) GetAuthHeaders() map[string]string {
	token := tm.GetAccessToken()
	if token == "" {
		return nil
	}

	return map[string]string{
		"X-Access-Token": token,
	}
}

// GetRefreshHeaders 获取刷新令牌头部
func (tm *TokenManager) GetRefreshHeaders() map[string]string {
	refreshToken := tm.GetRefreshToken()
	if refreshToken == "" {
		return nil
	}

	return map[string]string{
		"X-Refresh-Token": refreshToken,
	}
}

// LogTokenStatus 记录令牌状态（用于调试）
func (tm *TokenManager) LogTokenStatus() {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.tokenInfo == nil {
		slog.Info("Token status: No token")
		return
	}

	remaining := tm.GetRemainingTime()
	expired := tm.IsTokenExpired()
	shouldRefresh := tm.ShouldRefreshToken()

	slog.Info("Token status",
		"expired", expired,
		"shouldRefresh", shouldRefresh,
		"remaining", remaining)
}
