package api

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"time"
)

const (
	APP_VERSION = "1.0.0" // 应用版本
)

// TencentCloudSigner 腾讯云签名器
type TencentCloudSigner struct {
	SecretID  string
	SecretKey string
	BaseURL   string
}

// NewTencentCloudSigner 创建腾讯云签名器
func NewTencentCloudSigner(secretID, secretKey, baseURL string) *TencentCloudSigner {
	return &TencentCloudSigner{
		SecretID:  secretID,
		SecretKey: secretKey,
		BaseURL:   baseURL,
	}
}

// AddHeaders 添加腾讯云CAM鉴权头部
func (s *TencentCloudSigner) AddHeaders(req *http.Request, method, path, payload string) {
	// 生成签名
	algorithm := "TC3-HMAC-SHA256"
	service := "scf"
	timestamp := time.Now().Unix()
	
	// 步骤1: 构建规范请求字符串
	canonicalRequest := s.buildCanonicalRequest(method, path, "", s.BaseURL, payload)
	
	// 步骤2: 构建待签名字符串
	stringToSign := s.buildStringToSign(algorithm, timestamp, service, canonicalRequest)
	
	// 步骤3: 计算签名
	signature := s.calculateSignature(s.SecretKey, stringToSign, timestamp, service)
	
	// 步骤4: 构建授权头
	authorization := s.buildAuthorizationHeader(algorithm, s.SecretID, timestamp, service, "content-type;host", signature)

	req.Header.Set("Authorization", authorization)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Host", s.BaseURL)
	req.Header.Set("X-Scf-Cam-Timestamp", strconv.FormatInt(timestamp, 10))
	req.Header.Set("X-Scf-Cam-Uin", "100016998379")
	// 添加软件版本信息头
	req.Header.Set("X-App-Version", APP_VERSION)
}

func (s *TencentCloudSigner) buildCanonicalRequest(method, uri, queryString, host, payload string) string {
	canonicalHeaders := fmt.Sprintf("content-type:%s\nhost:%s\n", "application/json", host)
	signedHeaders := "content-type;host"
	hashedRequestPayload := s.sha256hex(payload)
	return fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
		method,
		uri,
		queryString,
		canonicalHeaders,
		signedHeaders,
		hashedRequestPayload)
}

func (s *TencentCloudSigner) buildStringToSign(algorithm string, timestamp int64, service, canonicalRequest string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	credentialScope := fmt.Sprintf("%s/%s/tc3_request", date, service)
	hashedCanonicalRequest := s.sha256hex(canonicalRequest)
	return fmt.Sprintf("%s\n%d\n%s\n%s",
		algorithm,
		timestamp,
		credentialScope,
		hashedCanonicalRequest)
}

func (s *TencentCloudSigner) calculateSignature(secretKey, stringToSign string, timestamp int64, service string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	secretDate := s.hmacsha256(date, "TC3"+secretKey)
	secretService := s.hmacsha256(service, secretDate)
	secretSigning := s.hmacsha256("tc3_request", secretService)
	return hex.EncodeToString([]byte(s.hmacsha256(stringToSign, secretSigning)))
}

func (s *TencentCloudSigner) buildAuthorizationHeader(algorithm, secretId string, timestamp int64, service, signedHeaders, signature string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	credentialScope := fmt.Sprintf("%s/%s/tc3_request", date, service)
	return fmt.Sprintf("%s Credential=%s/%s, SignedHeaders=%s, Signature=%s",
		algorithm,
		secretId,
		credentialScope,
		signedHeaders,
		signature)
}

func (s *TencentCloudSigner) sha256hex(str string) string {
	b := sha256.Sum256([]byte(str))
	return hex.EncodeToString(b[:])
}

func (s *TencentCloudSigner) hmacsha256(str, key string) string {
	hashed := hmac.New(sha256.New, []byte(key))
	hashed.Write([]byte(str))
	return string(hashed.Sum(nil))
}
