package api

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// Client API客户端主类
type Client struct {
	httpClient      *HTTPClient
	tokenManager    *TokenManager
	authService     *AuthService
	businessService *BusinessService
	ctx             context.Context
	config          HTTPConfig
}

// NewClient 创建新的API客户端
func NewClient(ctx context.Context, baseURL string) (*Client, error) {
	// 获取应用配置目录
	configDir, err := getConfigDir()
	if err != nil {
		return nil, fmt.Errorf("get config directory: %w", err)
	}

	// 创建HTTP配置
	config := HTTPConfig{
		BaseURL:    baseURL,
		Timeout:    30 * time.Second,
		Retries:    3,
		RetryDelay: 1 * time.Second,
		Headers: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
			"User-Agent":   "AI-Grading-Client/1.0",
		},
	}

	// 创建HTTP客户端
	httpClient := NewHTTPClient(config)

	// 创建Token管理器
	tokenManager := NewTokenManager(configDir)

	// 从文件加载Token
	if err := tokenManager.LoadFromFile(); err != nil {
		runtime.LogErrorf(ctx, "Failed to load tokens from file: %v", err)
	}

	// 创建服务
	authService := NewAuthService(httpClient, tokenManager, ctx)
	businessService := NewBusinessService(httpClient, tokenManager, ctx)

	client := &Client{
		httpClient:      httpClient,
		tokenManager:    tokenManager,
		authService:     authService,
		businessService: businessService,
		ctx:             ctx,
		config:          config,
	}

	runtime.LogInfof(ctx, "API client initialized with base URL: %s", baseURL)
	return client, nil
}

// ============= 认证相关方法 =============

// SendEmailCode 发送邮箱验证码
func (c *Client) SendEmailCode(email string) (*EmailCodeResponse, error) {
	return c.authService.SendEmailCode(email)
}

// EmailLogin 邮箱验证码登录
func (c *Client) EmailLogin(email, code string) (*LoginResponse, error) {
	response, err := c.authService.EmailLogin(email, code)
	if err != nil {
		return nil, err
	}

	// 自动刷新Token机制
	c.setupAutoRefresh()
	return response, nil
}

// Login 用户登录
func (c *Client) Login(email, password string) (*LoginResponse, error) {
	response, err := c.authService.Login(email, password)
	if err != nil {
		return nil, err
	}

	// 自动刷新Token机制
	c.setupAutoRefresh()
	return response, nil
}

// Recover 密码恢复
func (c *Client) Recover(email string) (*RecoverResponse, error) {
	return c.authService.Recover(email)
}

// RefreshToken 刷新访问令牌
func (c *Client) RefreshToken() (*LoginResponse, error) {
	return c.authService.RefreshToken()
}

// Register 用户注册
func (c *Client) Register(email, password string) (*RegisterResponse, error) {
	return c.authService.Register(email, password)
}

// UpdatePassword 更新密码
func (c *Client) UpdatePassword(password string) (*UpdatePasswordResponse, error) {
	return c.authService.UpdatePassword(password)
}

// Verify 验证令牌
func (c *Client) Verify(email, token, tokenType string) (*VerifyResponse, error) {
	return c.authService.Verify(email, token, tokenType)
}

// Logout 登出
func (c *Client) Logout() error {
	return c.authService.Logout()
}

// ============= 业务相关方法 =============

// GetAppVersion 获取应用版本信息
func (c *Client) GetAppVersion() (*AppVersionConfig, error) {
	return c.businessService.GetAppVersion()
}

// GetWalletBalance 查询钱包余额
func (c *Client) GetWalletBalance() (*WalletBalance, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.GetWalletBalance()
}

// SubmitRechargeRequest 提交充值申请
func (c *Client) SubmitRechargeRequest(amount float64, paymentMethod string) (*RechargeResponse, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.SubmitRechargeRequest(amount, paymentMethod)
}

// GetTOSCredentials 获取TOS临时凭证
func (c *Client) GetTOSCredentials() (*TOSCredentials, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.GetTOSCredentials()
}

// AnalyzeImage AI图像分析
func (c *Client) AnalyzeImage(image, criteria, subject, analysisMode string) (*AnalysisResponse, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.AnalyzeImage(image, criteria, subject, analysisMode)
}

// GetRechargeList 查询充值申请列表
func (c *Client) GetRechargeList() ([]RechargeResponse, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.GetRechargeList()
}

// GetTransactions 查询交易记录
func (c *Client) GetTransactions() ([]map[string]any, error) {
	if err := c.ensureAuthenticated(); err != nil {
		return nil, err
	}
	return c.businessService.GetTransactions()
}

// ============= 工具方法 =============

// IsAuthenticated 检查认证状态
func (c *Client) IsAuthenticated() bool {
	return c.authService.IsAuthenticated()
}

// GetAccessToken 获取访问令牌
func (c *Client) GetAccessToken() string {
	return c.authService.GetAccessToken()
}

// CheckConnection 检查API连接
func (c *Client) CheckConnection() bool {
	return c.businessService.CheckConnection()
}

// HealthCheck 健康检查
func (c *Client) HealthCheck() map[string]any {
	return c.businessService.HealthCheck()
}

// GetAuthStatus 获取认证状态详情
func (c *Client) GetAuthStatus() map[string]any {
	tokenInfo := c.tokenManager.GetTokenInfo()

	status := map[string]any{
		"isAuthenticated": c.IsAuthenticated(),
		"hasValidToken":   c.tokenManager.HasValidToken(),
		"shouldRefresh":   c.tokenManager.ShouldRefreshToken(),
		"tokenInfo":       nil,
	}

	if tokenInfo != nil {
		status["tokenInfo"] = map[string]any{
			"expiresAt":     tokenInfo.ExpiresAt,
			"expiresIn":     tokenInfo.ExpiresIn,
			"remainingTime": c.tokenManager.GetRemainingTime().String(),
		}
	}

	return status
}

// UpdateConfig 更新配置
func (c *Client) UpdateConfig(baseURL string, timeout time.Duration) {
	c.config.BaseURL = baseURL
	c.config.Timeout = timeout
	c.httpClient.UpdateConfig(c.config)

	runtime.LogInfof(c.ctx, "API config updated: baseURL=%s, timeout=%v", baseURL, timeout)
}

// ============= 私有方法 =============

// ensureAuthenticated 确保已认证，如果需要则自动刷新Token
func (c *Client) ensureAuthenticated() error {
	if !c.IsAuthenticated() {
		return fmt.Errorf("未认证，请先登录")
	}

	// 检查是否需要刷新Token
	if c.tokenManager.ShouldRefreshToken() {
		runtime.LogInfo(c.ctx, "Token needs refresh, auto refreshing...")
		if err := c.authService.AutoRefreshToken(); err != nil {
			runtime.LogErrorf(c.ctx, "Auto refresh token failed: %v", err)
			return fmt.Errorf("自动刷新Token失败，请重新登录")
		}
	}

	return nil
}

// setupAutoRefresh 设置自动刷新机制
func (c *Client) setupAutoRefresh() {
	// 这里可以设置定时器来自动刷新Token
	// 由于Wails的特性，我们在每次API调用前检查是否需要刷新
	runtime.LogInfo(c.ctx, "Auto refresh mechanism enabled")
}

// getConfigDir 获取配置目录
func getConfigDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}

	configDir := filepath.Join(homeDir, ".ai-grading")
	return configDir, nil
}
