package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// HTTPClient HTTP客户端
type HTTPClient struct {
	client *http.Client
	config HTTPConfig
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(config HTTPConfig) *HTTPClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.Retries == 0 {
		config.Retries = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = 1 * time.Second
	}
	if config.Headers == nil {
		config.Headers = make(map[string]string)
	}

	// 设置默认头部
	if config.Headers["Content-Type"] == "" {
		config.Headers["Content-Type"] = "application/json"
	}
	if config.Headers["Accept"] == "" {
		config.Headers["Accept"] = "application/json"
	}

	return &HTTPClient{
		client: &http.Client{
			Timeout: config.Timeout,
		},
		config: config,
	}
}

// Request 执行HTTP请求
func (c *HTTPClient) Request(ctx context.Context, method, path string, body any, headers map[string]string) (*http.Response, error) {
	url := c.buildURL(path)

	var bodyReader io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonData)
	}

	var lastErr error
	for attempt := 0; attempt <= c.config.Retries; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(c.config.RetryDelay):
			}
		}

		req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
		if err != nil {
			lastErr = fmt.Errorf("create request: %w", err)
			continue
		}

		// 设置头部
		for key, value := range c.config.Headers {
			req.Header.Set(key, value)
		}
		for key, value := range headers {
			req.Header.Set(key, value)
		}

		resp, err := c.client.Do(req)
		if err != nil {
			lastErr = &NetworkError{
				Message: fmt.Sprintf("HTTP request failed: %v", err),
				Err:     err,
			}
			continue
		}

		// 检查是否需要重试
		if c.shouldRetry(resp.StatusCode) && attempt < c.config.Retries {
			resp.Body.Close()
			lastErr = fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
			continue
		}

		return resp, nil
	}

	return nil, lastErr
}

// Get 执行GET请求
func (c *HTTPClient) Get(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	return c.Request(ctx, "GET", path, nil, headers)
}

// Post 执行POST请求
func (c *HTTPClient) Post(ctx context.Context, path string, body any, headers map[string]string) (*http.Response, error) {
	return c.Request(ctx, "POST", path, body, headers)
}

// Put 执行PUT请求
func (c *HTTPClient) Put(ctx context.Context, path string, body any, headers map[string]string) (*http.Response, error) {
	return c.Request(ctx, "PUT", path, body, headers)
}

// Delete 执行DELETE请求
func (c *HTTPClient) Delete(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	return c.Request(ctx, "DELETE", path, nil, headers)
}

// DecodeResponse 解析响应
func (c *HTTPClient) DecodeResponse(resp *http.Response, result any) error {
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return c.handleErrorResponse(resp)
	}

	if result == nil {
		return nil
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response body: %w", err)
	}

	if len(body) == 0 {
		return nil
	}

	if err := json.Unmarshal(body, result); err != nil {
		return fmt.Errorf("decode response: %w", err)
	}

	return nil
}

// RequestAndDecode 执行请求并解析响应
func (c *HTTPClient) RequestAndDecode(ctx context.Context, method, path string, body any, result any, headers map[string]string) error {
	resp, err := c.Request(ctx, method, path, body, headers)
	if err != nil {
		return err
	}

	return c.DecodeResponse(resp, result)
}

// buildURL 构建完整URL
func (c *HTTPClient) buildURL(path string) string {
	baseURL := strings.TrimRight(c.config.BaseURL, "/")
	path = strings.TrimLeft(path, "/")
	return fmt.Sprintf("%s/%s", baseURL, path)
}

// shouldRetry 判断是否应该重试
func (c *HTTPClient) shouldRetry(statusCode int) bool {
	// 只对服务器错误和网关错误重试
	return statusCode >= 500 || statusCode == 429
}

// handleErrorResponse 处理错误响应
func (c *HTTPClient) handleErrorResponse(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return &APIError{
			Message:    fmt.Sprintf("HTTP %d: %s", resp.StatusCode, resp.Status),
			StatusCode: resp.StatusCode,
		}
	}

	var errorResp ErrorResponse
	if err := json.Unmarshal(body, &errorResp); err != nil {
		return &APIError{
			Message:    fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			StatusCode: resp.StatusCode,
		}
	}

	message := errorResp.Message
	if message == "" {
		message = errorResp.Error
	}
	if message == "" {
		message = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	switch resp.StatusCode {
	case 401:
		return &AuthenticationError{Message: message}
	case 400:
		return &ValidationError{Message: message}
	default:
		return &APIError{
			Message:    message,
			StatusCode: resp.StatusCode,
			Code:       errorResp.Error,
		}
	}
}

// UpdateConfig 更新配置
func (c *HTTPClient) UpdateConfig(config HTTPConfig) {
	c.config = config
	c.client.Timeout = config.Timeout
}
