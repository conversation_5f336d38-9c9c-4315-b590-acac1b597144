package api

import (
	"context"
	"fmt"
	"log/slog"
)

// AuthService 认证服务
type AuthService struct {
	client       *HTTPClient
	tokenManager *TokenManager
	ctx          context.Context
}

// NewAuthService 创建新的认证服务
func NewAuthService(client *HTTPClient, tokenManager *TokenManager, ctx context.Context) *AuthService {
	return &AuthService{
		client:       client,
		tokenManager: tokenManager,
		ctx:          ctx,
	}
}

// SendEmailCode 发送邮箱验证码
func (as *AuthService) SendEmailCode(email string) (*EmailCodeResponse, error) {
	slog.Info("Sending email code", "email", email)
	
	request := &EmailCodeRequest{
		Email: email,
	}
	
	var response EmailCodeResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/email-code", request, &response, nil)
	if err != nil {
		slog.Error("Failed to send email code", "error", err)
		return nil, fmt.Errorf("发送邮箱验证码失败: %w", err)
	}
	
	slog.Info("Email code sent successfully")
	return &response, nil
}

// EmailLogin 邮箱验证码登录
func (as *AuthService) EmailLogin(email, code string) (*LoginResponse, error) {
	slog.Info("Email login", "email", email)
	
	request := &EmailLoginRequest{
		Email: email,
		Code:  code,
	}
	
	var response LoginResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/email-login", request, &response, nil)
	if err != nil {
		slog.Error("Email login failed", "error", err)
		return nil, fmt.Errorf("邮箱验证码登录失败: %w", err)
	}
	
	// 保存令牌
	as.tokenManager.SetTokensFromResponse(&response)
	
	slog.Info("Email login successful")
	return &response, nil
}

// Login 用户登录
func (as *AuthService) Login(email, password string) (*LoginResponse, error) {
	slog.Info("User login", "email", email)
	
	request := &LoginRequest{
		Email:    email,
		Password: password,
	}
	
	var response LoginResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/login", request, &response, nil)
	if err != nil {
		slog.Error("Login failed", "error", err)
		return nil, fmt.Errorf("用户登录失败: %w", err)
	}
	
	// 保存令牌
	as.tokenManager.SetTokensFromResponse(&response)
	
	slog.Info("User login successful")
	return &response, nil
}

// Recover 密码恢复
func (as *AuthService) Recover(email string) (*RecoverResponse, error) {
	slog.Info("Password recovery", "email", email)
	
	request := &RecoverRequest{
		Email: email,
	}
	
	var response RecoverResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/recover", request, &response, nil)
	if err != nil {
		slog.Error("Password recovery failed", "error", err)
		return nil, fmt.Errorf("密码恢复失败: %w", err)
	}
	
	slog.Info("Password recovery email sent")
	return &response, nil
}

// RefreshToken 刷新访问令牌
func (as *AuthService) RefreshToken() (*LoginResponse, error) {
	slog.Info("Refreshing access token")
	
	refreshToken := as.tokenManager.GetRefreshToken()
	if refreshToken == "" {
		return nil, fmt.Errorf("没有可用的刷新令牌")
	}
	
	headers := as.tokenManager.GetRefreshHeaders()
	
	var response LoginResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/refresh", nil, &response, headers)
	if err != nil {
		slog.Error("Token refresh failed", "error", err)
		// 刷新失败，清除令牌
		as.tokenManager.ClearTokens()
		return nil, fmt.Errorf("刷新访问令牌失败: %w", err)
	}
	
	// 更新令牌
	as.tokenManager.UpdateTokensFromResponse(&response)
	
	slog.Info("Token refresh successful")
	return &response, nil
}

// Register 用户注册
func (as *AuthService) Register(email, password string) (*RegisterResponse, error) {
	slog.Info("User registration", "email", email)
	
	request := &RegisterRequest{
		Email:    email,
		Password: password,
	}
	
	var response RegisterResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/register", request, &response, nil)
	if err != nil {
		slog.Error("Registration failed", "error", err)
		return nil, fmt.Errorf("用户注册失败: %w", err)
	}
	
	slog.Info("User registration successful")
	return &response, nil
}

// UpdatePassword 更新密码
func (as *AuthService) UpdatePassword(password string) (*UpdatePasswordResponse, error) {
	slog.Info("Updating password")
	
	request := &UpdatePasswordRequest{
		Password: password,
	}
	
	headers := as.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法更新密码")
	}
	
	var response UpdatePasswordResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/update-password", request, &response, headers)
	if err != nil {
		slog.Error("Password update failed", "error", err)
		return nil, fmt.Errorf("更新密码失败: %w", err)
	}
	
	slog.Info("Password updated successfully")
	return &response, nil
}

// Verify 验证令牌
func (as *AuthService) Verify(email, token, tokenType string) (*VerifyResponse, error) {
	slog.Info("Verifying token", "email", email, "type", tokenType)
	
	request := &VerifyRequest{
		Email: email,
		Token: token,
		Type:  tokenType,
	}
	
	var response VerifyResponse
	err := as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/verify", request, &response, nil)
	if err != nil {
		slog.Error("Token verification failed", "error", err)
		return nil, fmt.Errorf("验证令牌失败: %w", err)
	}
	
	// 如果验证成功并返回了新令牌，保存它们
	if response.AccessToken != "" && response.RefreshToken != "" {
		tokenInfo := &TokenInfo{
			AccessToken:  response.AccessToken,
			RefreshToken: response.RefreshToken,
			ExpiresAt:    response.ExpiresAt,
			ExpiresIn:    response.ExpiresIn,
		}
		
		as.tokenManager.SetTokens(tokenInfo)
	}
	
	slog.Info("Token verification successful")
	return &response, nil
}

// Logout 登出
func (as *AuthService) Logout() error {
	slog.Info("User logout")
	
	// 清除本地令牌
	as.tokenManager.ClearTokens()
	
	// 这里可以添加调用后端登出API的逻辑
	// headers := as.tokenManager.GetAuthHeaders()
	// if headers != nil {
	//     as.client.RequestAndDecode(as.ctx, "POST", "/api/v1/auth/logout", nil, nil, headers)
	// }
	
	slog.Info("User logout successful")
	return nil
}

// IsAuthenticated 检查认证状态
func (as *AuthService) IsAuthenticated() bool {
	return as.tokenManager.HasValidToken()
}

// GetAccessToken 获取当前访问令牌
func (as *AuthService) GetAccessToken() string {
	return as.tokenManager.GetAccessToken()
}

// ShouldRefreshToken 检查是否需要刷新令牌
func (as *AuthService) ShouldRefreshToken() bool {
	return as.tokenManager.ShouldRefreshToken()
}

// AutoRefreshToken 自动刷新令牌（如果需要）
func (as *AuthService) AutoRefreshToken() error {
	if !as.ShouldRefreshToken() {
		return nil
	}
	
	slog.Info("Auto refreshing token")
	_, err := as.RefreshToken()
	return err
}

// GetAuthHeaders 获取认证头部
func (as *AuthService) GetAuthHeaders() map[string]string {
	return as.tokenManager.GetAuthHeaders()
}
