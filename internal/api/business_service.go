package api

import (
	"context"
	"fmt"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// BusinessService 业务服务
type BusinessService struct {
	client       *HTTPClient
	tokenManager *TokenManager
	ctx          context.Context
}

// NewBusinessService 创建新的业务服务
func NewBusinessService(client *HTTPClient, tokenManager *TokenManager, ctx context.Context) *BusinessService {
	return &BusinessService{
		client:       client,
		tokenManager: tokenManager,
		ctx:          ctx,
	}
}

// GetAppVersion 获取应用版本信息
func (bs *BusinessService) GetAppVersion() (*AppVersionConfig, error) {
	runtime.LogInfo(bs.ctx, "Getting app version")

	var response AppVersionConfig
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/version", nil, &response, nil)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to get app version: %v", err)
		return nil, fmt.Errorf("获取应用版本信息失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "App version: %s", response.Version)
	return &response, nil
}

// GetWalletBalance 查询钱包余额
func (bs *BusinessService) GetWalletBalance() (*WalletBalance, error) {
	runtime.LogInfo(bs.ctx, "Getting wallet balance")

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询钱包余额")
	}

	var response WalletBalance
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/wallet/balance", nil, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to get wallet balance: %v", err)
		return nil, fmt.Errorf("查询钱包余额失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "Wallet balance: %.2f %s", response.Balance, response.Currency)
	return &response, nil
}

// SubmitRechargeRequest 提交充值申请
func (bs *BusinessService) SubmitRechargeRequest(amount float64, paymentMethod string) (*RechargeResponse, error) {
	runtime.LogInfof(bs.ctx, "Submitting recharge request: amount=%.2f, method=%s", amount, paymentMethod)

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法提交充值申请")
	}

	request := &RechargeRequest{
		Amount:        amount,
		PaymentMethod: paymentMethod,
	}

	var response RechargeResponse
	err := bs.client.RequestAndDecode(bs.ctx, "POST", "/api/v1/recharge/request", request, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to submit recharge request: %v", err)
		return nil, fmt.Errorf("提交充值申请失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "Recharge request submitted: ID=%s", response.RequestID)
	return &response, nil
}

// GetTOSCredentials 获取TOS临时凭证
func (bs *BusinessService) GetTOSCredentials() (*TOSCredentials, error) {
	runtime.LogInfo(bs.ctx, "Getting TOS credentials")

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法获取TOS凭证")
	}

	var response TOSCredentials
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/tos/credentials", nil, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to get TOS credentials: %v", err)
		return nil, fmt.Errorf("获取TOS临时凭证失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "TOS credentials obtained: region=%s, bucket=%s", response.Region, response.Bucket)
	return &response, nil
}

// AnalyzeImage AI图像分析
func (bs *BusinessService) AnalyzeImage(image, criteria, subject, analysisMode string) (*AnalysisResponse, error) {
	runtime.LogInfof(bs.ctx, "Analyzing image: subject=%s, mode=%s", subject, analysisMode)

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法进行AI图像分析")
	}

	request := &AnalysisRequest{
		Image:        image,
		Criteria:     criteria,
		Subject:      subject,
		AnalysisMode: analysisMode,
	}

	var response AnalysisResponse
	err := bs.client.RequestAndDecode(bs.ctx, "POST", "/api/v2/chat/analysis", request, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to analyze image: %v", err)
		return nil, fmt.Errorf("AI图像分析失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "Image analysis completed: score=%d", response.Score)
	return &response, nil
}

// GetRechargeList 查询充值申请列表
func (bs *BusinessService) GetRechargeList() ([]RechargeResponse, error) {
	runtime.LogInfo(bs.ctx, "Getting recharge list")

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询充值申请列表")
	}

	var response []RechargeResponse
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/recharge/list", nil, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to get recharge list: %v", err)
		return nil, fmt.Errorf("查询充值申请列表失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "Recharge list retrieved: %d items", len(response))
	return response, nil
}

// GetTransactions 查询交易记录
func (bs *BusinessService) GetTransactions() ([]map[string]any, error) {
	runtime.LogInfo(bs.ctx, "Getting transactions")

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询交易记录")
	}

	var response []map[string]any
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/wallet/transactions", nil, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to get transactions: %v", err)
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	runtime.LogInfof(bs.ctx, "Transactions retrieved: %d items", len(response))
	return response, nil
}

// TestProtectedAPI 受保护的API测试
func (bs *BusinessService) TestProtectedAPI() (map[string]any, error) {
	runtime.LogInfo(bs.ctx, "Testing protected API")

	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法访问受保护的API")
	}

	var response map[string]any
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/protected", nil, &response, headers)
	if err != nil {
		runtime.LogErrorf(bs.ctx, "Failed to test protected API: %v", err)
		return nil, fmt.Errorf("受保护的API测试失败: %w", err)
	}

	runtime.LogInfo(bs.ctx, "Protected API test successful")
	return response, nil
}

// CheckConnection 检查API连接状态
func (bs *BusinessService) CheckConnection() bool {
	runtime.LogInfo(bs.ctx, "Checking API connection")

	_, err := bs.GetAppVersion()
	if err != nil {
		runtime.LogErrorf(bs.ctx, "API connection check failed: %v", err)
		return false
	}

	runtime.LogInfo(bs.ctx, "API connection check successful")
	return true
}

// HealthCheck 健康检查
func (bs *BusinessService) HealthCheck() map[string]any {
	runtime.LogInfo(bs.ctx, "Performing health check")

	result := map[string]any{
		"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		"status":    "unhealthy",
	}

	version, err := bs.GetAppVersion()
	if err != nil {
		result["error"] = err.Error()
		runtime.LogErrorf(bs.ctx, "Health check failed: %v", err)
		return result
	}

	result["status"] = "healthy"
	result["version"] = version.Version

	runtime.LogInfo(bs.ctx, "Health check successful")
	return result
}
