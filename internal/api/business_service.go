package api

import (
	"context"
	"fmt"
	"log/slog"
	"time"
)

// BusinessService 业务服务
type BusinessService struct {
	client       *HTTPClient
	tokenManager *TokenManager
	ctx          context.Context
}

// NewBusinessService 创建新的业务服务
func NewBusinessService(client *HTTPClient, tokenManager *TokenManager, ctx context.Context) *BusinessService {
	return &BusinessService{
		client:       client,
		tokenManager: tokenManager,
		ctx:          ctx,
	}
}

// GetAppVersion 获取应用版本信息
func (bs *BusinessService) GetAppVersion() (*AppVersionConfig, error) {
	slog.Info("Getting app version")
	
	var response AppVersionConfig
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/version", nil, &response, nil)
	if err != nil {
		slog.Error("Failed to get app version", "error", err)
		return nil, fmt.Errorf("获取应用版本信息失败: %w", err)
	}
	
	slog.Info("App version retrieved", "version", response.Version)
	return &response, nil
}

// GetWalletBalance 查询钱包余额
func (bs *BusinessService) GetWalletBalance() (*WalletBalance, error) {
	slog.Info("Getting wallet balance")
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询钱包余额")
	}
	
	var response WalletBalance
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/wallet/balance", nil, &response, headers)
	if err != nil {
		slog.Error("Failed to get wallet balance", "error", err)
		return nil, fmt.Errorf("查询钱包余额失败: %w", err)
	}
	
	slog.Info("Wallet balance retrieved", "balance", response.Balance, "currency", response.Currency)
	return &response, nil
}

// SubmitRechargeRequest 提交充值申请
func (bs *BusinessService) SubmitRechargeRequest(amount float64, paymentMethod string) (*RechargeResponse, error) {
	slog.Info("Submitting recharge request", "amount", amount, "method", paymentMethod)
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法提交充值申请")
	}
	
	request := &RechargeRequest{
		Amount:        amount,
		PaymentMethod: paymentMethod,
	}
	
	var response RechargeResponse
	err := bs.client.RequestAndDecode(bs.ctx, "POST", "/api/v1/recharge/request", request, &response, headers)
	if err != nil {
		slog.Error("Failed to submit recharge request", "error", err)
		return nil, fmt.Errorf("提交充值申请失败: %w", err)
	}
	
	slog.Info("Recharge request submitted", "requestID", response.RequestID)
	return &response, nil
}

// GetTOSCredentials 获取TOS临时凭证
func (bs *BusinessService) GetTOSCredentials() (*TOSCredentials, error) {
	slog.Info("Getting TOS credentials")
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法获取TOS凭证")
	}
	
	var response TOSCredentials
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/tos/credentials", nil, &response, headers)
	if err != nil {
		slog.Error("Failed to get TOS credentials", "error", err)
		return nil, fmt.Errorf("获取TOS临时凭证失败: %w", err)
	}
	
	slog.Info("TOS credentials obtained", "region", response.Region, "bucket", response.Bucket)
	return &response, nil
}

// AnalyzeImage AI图像分析
func (bs *BusinessService) AnalyzeImage(image, criteria, subject, analysisMode string) (*AnalysisResponse, error) {
	slog.Info("Analyzing image", "subject", subject, "mode", analysisMode)
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法进行AI图像分析")
	}
	
	request := &AnalysisRequest{
		Image:        image,
		Criteria:     criteria,
		Subject:      subject,
		AnalysisMode: analysisMode,
	}
	
	var response AnalysisResponse
	err := bs.client.RequestAndDecode(bs.ctx, "POST", "/api/v2/chat/analysis", request, &response, headers)
	if err != nil {
		slog.Error("Failed to analyze image", "error", err)
		return nil, fmt.Errorf("AI图像分析失败: %w", err)
	}
	
	slog.Info("Image analysis completed", "score", response.Score)
	return &response, nil
}

// GetRechargeList 查询充值申请列表
func (bs *BusinessService) GetRechargeList() ([]RechargeResponse, error) {
	slog.Info("Getting recharge list")
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询充值申请列表")
	}
	
	var response []RechargeResponse
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/recharge/list", nil, &response, headers)
	if err != nil {
		slog.Error("Failed to get recharge list", "error", err)
		return nil, fmt.Errorf("查询充值申请列表失败: %w", err)
	}
	
	slog.Info("Recharge list retrieved", "count", len(response))
	return response, nil
}

// GetTransactions 查询交易记录
func (bs *BusinessService) GetTransactions() ([]map[string]any, error) {
	slog.Info("Getting transactions")
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法查询交易记录")
	}
	
	var response []map[string]any
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/wallet/transactions", nil, &response, headers)
	if err != nil {
		slog.Error("Failed to get transactions", "error", err)
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}
	
	slog.Info("Transactions retrieved", "count", len(response))
	return response, nil
}

// TestProtectedAPI 受保护的API测试
func (bs *BusinessService) TestProtectedAPI() (map[string]any, error) {
	slog.Info("Testing protected API")
	
	headers := bs.tokenManager.GetAuthHeaders()
	if headers == nil {
		return nil, fmt.Errorf("未认证，无法访问受保护的API")
	}
	
	var response map[string]any
	err := bs.client.RequestAndDecode(bs.ctx, "GET", "/api/v1/protected", nil, &response, headers)
	if err != nil {
		slog.Error("Failed to test protected API", "error", err)
		return nil, fmt.Errorf("受保护的API测试失败: %w", err)
	}
	
	slog.Info("Protected API test successful")
	return response, nil
}

// CheckConnection 检查API连接状态
func (bs *BusinessService) CheckConnection() bool {
	slog.Info("Checking API connection")
	
	_, err := bs.GetAppVersion()
	if err != nil {
		slog.Error("API connection check failed", "error", err)
		return false
	}
	
	slog.Info("API connection check successful")
	return true
}

// HealthCheck 健康检查
func (bs *BusinessService) HealthCheck() map[string]any {
	slog.Info("Performing health check")
	
	result := map[string]any{
		"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		"status":    "unhealthy",
	}
	
	version, err := bs.GetAppVersion()
	if err != nil {
		result["error"] = err.Error()
		slog.Error("Health check failed", "error", err)
		return result
	}
	
	result["status"] = "healthy"
	result["version"] = version.Version
	
	slog.Info("Health check successful")
	return result
}
