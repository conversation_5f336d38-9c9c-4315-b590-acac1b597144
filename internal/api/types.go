package api

import "time"

// ============= 基础类型 =============

// APIResponse 通用API响应结构
type APIResponse[T any] struct {
	Success bool   `json:"success,omitempty"`
	Message string `json:"message,omitempty"`
	Data    T      `json:"data,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error,omitempty"`
	Message string `json:"message,omitempty"`
}

// ============= 认证相关类型 =============

// EmailCodeRequest 发送邮箱验证码请求
type EmailCodeRequest struct {
	Email string `json:"email"`
}

// EmailCodeResponse 发送邮箱验证码响应
type EmailCodeResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// EmailLoginRequest 邮箱验证码登录请求
type EmailLoginRequest struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

// LoginRequest 用户登录请求
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// LoginResponse 用户登录响应
type LoginResponse struct {
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int          `json:"expires_in"`
	ExpiresAt    int64        `json:"expires_at"`
	Config       []ConfigItem `json:"config"`
	Subjects     []Subject    `json:"subjects"`
}

// RecoverRequest 密码恢复请求
type RecoverRequest struct {
	Email string `json:"email"`
}

// RecoverResponse 密码恢复响应
type RecoverResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// RegisterRequest 用户注册请求
type RegisterRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegisterResponse 用户注册响应
type RegisterResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// UpdatePasswordRequest 更新密码请求
type UpdatePasswordRequest struct {
	Password string `json:"password"`
}

// UpdatePasswordResponse 更新密码响应
type UpdatePasswordResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// VerifyRequest 验证令牌请求
type VerifyRequest struct {
	Email string `json:"email"`
	Token string `json:"token"`
	Type  string `json:"type"`
}

// VerifyResponse 验证令牌响应
type VerifyResponse struct {
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresIn    int    `json:"expires_in,omitempty"`
	ExpiresAt    int64  `json:"expires_at,omitempty"`
	Message      string `json:"message"`
	Success      bool   `json:"success"`
}

// ============= 配置相关类型 =============

// ConfigItem 配置项
type ConfigItem struct {
	ID      string   `json:"id"`
	Name    string   `json:"name"`
	URL     string   `json:"url"`
	Actions []Action `json:"actions"`
}

// Action 操作配置
type Action struct {
	Type     string `json:"type"`
	Selector string `json:"selector"`
	Index    int    `json:"index"`
	Value    string `json:"value,omitempty"`
}

// Subject 学科信息
type Subject struct {
	SubjectID   string `json:"subject_id"`
	SubjectName string `json:"subject_name"`
	PromptText  string `json:"prompt_text"`
}

// ============= 应用版本类型 =============

// AppVersionConfig 应用版本信息
type AppVersionConfig struct {
	Version     string `json:"version"`
	DownloadURL string `json:"download_url"`
	UpdateLog   string `json:"update_log"`
	ForceUpdate bool   `json:"force_update"`
}

// ============= 钱包相关类型 =============

// WalletBalance 钱包余额
type WalletBalance struct {
	Balance     float64   `json:"balance"`
	Currency    string    `json:"currency"`
	LastUpdated time.Time `json:"last_updated"`
}

// RechargeRequest 充值申请请求
type RechargeRequest struct {
	Amount        float64 `json:"amount"`
	PaymentMethod string  `json:"payment_method,omitempty"`
}

// RechargeResponse 充值申请响应
type RechargeResponse struct {
	RequestID string    `json:"request_id"`
	Amount    float64   `json:"amount"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

// ============= TOS 凭证类型 =============

// TOSCredentials TOS临时凭证
type TOSCredentials struct {
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	SessionToken    string `json:"session_token"`
	Region          string `json:"region"`
	Bucket          string `json:"bucket"`
	ExpiresAt       int64  `json:"expires_at"`
}

// ============= AI 分析类型 =============

// AnalysisRequest AI分析请求
type AnalysisRequest struct {
	Image        string `json:"image"`        // base64编码的图片
	Criteria     string `json:"criteria,omitempty"`
	Subject      string `json:"subject,omitempty"`
	AnalysisMode string `json:"analysis_mode,omitempty"`
}

// AnalysisResponse AI分析响应
type AnalysisResponse struct {
	StudentAnswer   string    `json:"student_answer"`
	Score           int       `json:"score"`
	GradingDetails  string    `json:"grading_details"`
	AnalysisID      string    `json:"analysis_id,omitempty"`
	CreatedAt       time.Time `json:"created_at,omitempty"`
}

// ============= Token 管理类型 =============

// TokenInfo Token信息
type TokenInfo struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
	ExpiresIn    int    `json:"expires_in"`
}

// ============= HTTP 配置类型 =============

// HTTPConfig HTTP客户端配置
type HTTPConfig struct {
	BaseURL    string            `json:"base_url"`
	Timeout    time.Duration     `json:"timeout"`
	Retries    int               `json:"retries"`
	RetryDelay time.Duration     `json:"retry_delay"`
	Headers    map[string]string `json:"headers"`
}

// ============= 错误类型 =============

// APIError API错误
type APIError struct {
	Message    string `json:"message"`
	StatusCode int    `json:"status_code"`
	Code       string `json:"code"`
}

func (e *APIError) Error() string {
	return e.Message
}

// NetworkError 网络错误
type NetworkError struct {
	Message string `json:"message"`
	Err     error  `json:"-"`
}

func (e *NetworkError) Error() string {
	return e.Message
}

// AuthenticationError 认证错误
type AuthenticationError struct {
	Message string `json:"message"`
}

func (e *AuthenticationError) Error() string {
	return e.Message
}

// ValidationError 验证错误
type ValidationError struct {
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return e.Message
}
