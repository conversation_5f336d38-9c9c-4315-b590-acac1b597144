/**
 * API 配置文件
 * 管理API相关的配置信息
 */

import type { ApiClientConfig } from './types'

/**
 * 默认API配置
 */
export const DEFAULT_API_CONFIG: ApiClientConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.example.com',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  defaultHeaders: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

/**
 * 开发环境配置
 */
export const DEV_API_CONFIG: Partial<ApiClientConfig> = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 60000, // 开发环境延长超时时间
  retries: 1, // 开发环境减少重试次数
  defaultHeaders: {
    ...DEFAULT_API_CONFIG.defaultHeaders,
    'X-Debug': 'true'
  }
}

/**
 * 生产环境配置
 */
export const PROD_API_CONFIG: Partial<ApiClientConfig> = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.production.com',
  timeout: 30000,
  retries: 3,
  retryDelay: 2000, // 生产环境增加重试延迟
  defaultHeaders: {
    ...DEFAULT_API_CONFIG.defaultHeaders
  }
}

/**
 * 测试环境配置
 */
export const TEST_API_CONFIG: Partial<ApiClientConfig> = {
  baseURL: 'http://localhost:3000',
  timeout: 10000,
  retries: 0, // 测试环境不重试
  defaultHeaders: {
    ...DEFAULT_API_CONFIG.defaultHeaders,
    'X-Test': 'true'
  }
}

/**
 * 根据环境获取API配置
 */
export function getApiConfig(): ApiClientConfig {
  const env = import.meta.env.MODE || 'development'
  
  switch (env) {
    case 'development':
      return { ...DEFAULT_API_CONFIG, ...DEV_API_CONFIG }
    case 'production':
      return { ...DEFAULT_API_CONFIG, ...PROD_API_CONFIG }
    case 'test':
      return { ...DEFAULT_API_CONFIG, ...TEST_API_CONFIG }
    default:
      return DEFAULT_API_CONFIG
  }
}

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    EMAIL_CODE: '/api/v1/auth/email-code',
    EMAIL_LOGIN: '/api/v1/auth/email-login',
    LOGIN: '/api/v1/auth/login',
    RECOVER: '/api/v1/auth/recover',
    REFRESH: '/api/v1/auth/refresh',
    REGISTER: '/api/v1/auth/register',
    UPDATE_PASSWORD: '/api/v1/auth/update-password',
    VERIFY: '/api/v1/auth/verify'
  },
  
  // 业务相关
  BUSINESS: {
    VERSION: '/api/v1/version',
    WALLET_BALANCE: '/api/v1/wallet/balance',
    WALLET_TRANSACTIONS: '/api/v1/wallet/transactions',
    RECHARGE_REQUEST: '/api/v1/recharge/request',
    RECHARGE_LIST: '/api/v1/recharge/list',
    TOS_CREDENTIALS: '/api/v1/tos/credentials',
    PROTECTED: '/api/v1/protected'
  },
  
  // AI服务
  AI: {
    ANALYSIS: '/api/v2/chat/analysis'
  },
  
  // 管理员相关
  ADMIN: {
    USER_LIST: '/api/v1/admin/user/list',
    USER_INFO: '/api/v1/admin/user/info',
    USER_CREATE: '/api/v1/admin/user/create',
    USER_BALANCE_ADJUST: '/api/v1/admin/user/balance/adjust',
    RECHARGE_REQUESTS: '/api/v1/admin/recharge/requests',
    RECHARGE_PROCESS: '/api/v1/admin/recharge/process',
    FINANCE_EXPENSE: '/api/v1/admin/finance/expense',
    FINANCE_EXPENSES: '/api/v1/admin/finance/expenses',
    FINANCE_ANALYSIS: '/api/v1/admin/finance/analysis',
    FINANCE_REPORT: '/api/v1/admin/finance/report'
  }
} as const

/**
 * HTTP状态码配置
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const

/**
 * 错误消息配置
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  AUTHENTICATION_ERROR: '认证失败，请重新登录',
  AUTHORIZATION_ERROR: '权限不足，无法访问该资源',
  VALIDATION_ERROR: '请求参数错误，请检查输入',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请联系技术支持'
} as const

/**
 * 请求配置预设
 */
export const REQUEST_PRESETS = {
  // 快速请求（短超时）
  FAST: {
    timeout: 5000,
    retries: 1,
    retryDelay: 500
  },
  
  // 标准请求
  STANDARD: {
    timeout: 30000,
    retries: 3,
    retryDelay: 1000
  },
  
  // 长时间请求（如文件上传）
  LONG: {
    timeout: 120000,
    retries: 2,
    retryDelay: 2000
  },
  
  // 关键请求（更多重试）
  CRITICAL: {
    timeout: 30000,
    retries: 5,
    retryDelay: 2000
  }
} as const

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  // 默认缓存时间（毫秒）
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  
  // 不同类型数据的缓存时间
  TTL: {
    USER_INFO: 10 * 60 * 1000, // 10分钟
    APP_VERSION: 30 * 60 * 1000, // 30分钟
    WALLET_BALANCE: 1 * 60 * 1000, // 1分钟
    TOS_CREDENTIALS: 50 * 60 * 1000 // 50分钟（临时凭证通常1小时过期）
  }
} as const

/**
 * 验证配置
 */
export const VALIDATION_CONFIG = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MAX_LENGTH: 254
  },
  
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    PATTERN: /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/ // 至少包含字母和数字
  },
  
  VERIFICATION_CODE: {
    LENGTH: 6,
    PATTERN: /^\d{6}$/
  }
} as const
