/**
 * API客户端测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ApiClient } from '../api-client'
import { ApiTokenManager } from '../token-manager'

// Mock fetch
global.fetch = vi.fn()

describe('ApiClient', () => {
  let apiClient: ApiClient
  let tokenManager: ApiTokenManager

  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    apiClient = new ApiClient({
      baseURL: 'https://test-api.example.com',
      timeout: 5000
    })
    
    tokenManager = new ApiTokenManager()
  })

  describe('认证功能', () => {
    it('应该能够成功登录', async () => {
      const mockResponse = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 3600,
        expires_at: Math.floor(Date.now() / 1000) + 3600,
        config: [],
        subjects: []
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const result = await apiClient.auth.login({
        email: '<EMAIL>',
        password: 'password123'
      })

      expect(result).toEqual(mockResponse)
      expect(tokenManager.getAccessToken()).toBe('test-access-token')
      expect(tokenManager.getRefreshToken()).toBe('test-refresh-token')
    })

    it('应该能够处理登录失败', async () => {
      const mockError = {
        message: '用户名或密码错误',
        error: 'INVALID_CREDENTIALS'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => mockError,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      await expect(
        apiClient.auth.login({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
      ).rejects.toThrow('用户名或密码错误')
    })

    it('应该能够自动刷新令牌', async () => {
      // 设置即将过期的令牌
      const expiresAt = Math.floor(Date.now() / 1000) + 60 // 1分钟后过期
      tokenManager.setTokens({
        accessToken: 'old-access-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 60,
        expiresAt
      })

      const mockRefreshResponse = {
        access_token: 'new-access-token',
        expires_in: 3600,
        expires_at: Math.floor(Date.now() / 1000) + 3600
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRefreshResponse,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const result = await apiClient.auth.refreshToken()

      expect(result.access_token).toBe('new-access-token')
      expect(tokenManager.getAccessToken()).toBe('new-access-token')
    })
  })

  describe('业务功能', () => {
    beforeEach(() => {
      // 设置有效的令牌
      tokenManager.setTokens({
        accessToken: 'valid-access-token',
        refreshToken: 'valid-refresh-token',
        expiresIn: 3600,
        expiresAt: Math.floor(Date.now() / 1000) + 3600
      })
    })

    it('应该能够获取应用版本', async () => {
      const mockVersion = {
        version: 'v1.0.0',
        download_url: 'https://example.com/download',
        update_log: '修复了一些问题',
        force_update: false
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockVersion,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const result = await apiClient.business.getAppVersion()

      expect(result).toEqual(mockVersion)
      expect(fetch).toHaveBeenCalledWith(
        'https://test-api.example.com/api/v1/version',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'X-Access-Token': 'valid-access-token'
          })
        })
      )
    })

    it('应该能够获取钱包余额', async () => {
      const mockBalance = {
        balance: 100.50,
        currency: 'CNY',
        last_updated: '2023-12-01T10:00:00Z'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockBalance,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const result = await apiClient.business.getWalletBalance()

      expect(result).toEqual(mockBalance)
    })

    it('应该能够进行AI图像分析', async () => {
      const mockAnalysis = {
        student_answer: '这是学生的答案',
        score: 85,
        grading_details: '答案基本正确，但有小错误'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalysis,
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const result = await apiClient.business.analyzeImage({
        image: 'base64-image-data',
        criteria: '评分标准',
        subject: '数学'
      })

      expect(result).toEqual(mockAnalysis)
    })
  })

  describe('错误处理', () => {
    it('应该能够处理网络错误', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      await expect(
        apiClient.business.getAppVersion()
      ).rejects.toThrow('网络请求失败')
    })

    it('应该能够处理401错误并清除令牌', async () => {
      // 设置令牌
      tokenManager.setTokens({
        accessToken: 'invalid-token',
        refreshToken: 'invalid-refresh-token',
        expiresIn: 3600,
        expiresAt: Math.floor(Date.now() / 1000) + 3600
      })

      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ message: 'Unauthorized' }),
        headers: new Headers({ 'content-type': 'application/json' })
      })

      await expect(
        apiClient.business.getWalletBalance()
      ).rejects.toThrow('Unauthorized')

      // 验证令牌已被清除
      expect(tokenManager.getAccessToken()).toBeNull()
    })
  })

  describe('配置管理', () => {
    it('应该能够更新配置', () => {
      const newConfig = {
        baseURL: 'https://new-api.example.com',
        timeout: 60000
      }

      apiClient.updateConfig(newConfig)
      const config = apiClient.getConfig()

      expect(config.baseURL).toBe('https://new-api.example.com')
      expect(config.timeout).toBe(60000)
    })

    it('应该能够检查连接状态', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ version: 'v1.0.0' }),
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const isConnected = await apiClient.checkConnection()
      expect(isConnected).toBe(true)
    })

    it('应该能够进行健康检查', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ version: 'v1.0.0' }),
        headers: new Headers({ 'content-type': 'application/json' })
      })

      const health = await apiClient.healthCheck()
      
      expect(health.status).toBe('healthy')
      expect(health.details.version).toBe('v1.0.0')
      expect(typeof health.timestamp).toBe('number')
    })
  })
})
