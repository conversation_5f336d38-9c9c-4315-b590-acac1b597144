# API 服务文档

本文档介绍如何使用高可维护性的API调用服务。

## 目录结构

```
src/services/api/
├── index.ts              # 主入口文件
├── api-client.ts         # API客户端主类
├── auth-service.ts       # 认证服务
├── business-service.ts   # 业务服务
├── http-client.ts        # HTTP客户端基础类
├── token-manager.ts      # Token管理器
├── types.ts              # 类型定义
├── config.ts             # 配置文件
├── examples.ts           # 使用示例
└── README.md             # 本文档
```

## 快速开始

### 1. 基本使用

```typescript
import api from '@/services/api'

// 用户登录
const response = await api.login('<EMAIL>', 'password123')

// 获取钱包余额
const balance = await api.getWalletBalance()

// AI图像分析
const result = await api.analyzeImage(imageBase64, criteria, subject)
```

### 2. 使用服务实例

```typescript
import { authService, businessService } from '@/services/api'

// 认证服务
const loginResponse = await authService.login({
  email: '<EMAIL>',
  password: 'password123'
})

// 业务服务
const version = await businessService.getAppVersion()
```

## 主要功能

### 认证管理

- ✅ 自动Token管理（存储、刷新、过期检查）
- ✅ 多种登录方式（密码登录、邮箱验证码登录）
- ✅ 密码重置流程
- ✅ 自动刷新Token机制

### 网络请求

- ✅ 统一的HTTP客户端
- ✅ 请求/响应拦截器
- ✅ 自动重试机制
- ✅ 超时控制
- ✅ 错误处理

### 类型安全

- ✅ 完整的TypeScript类型定义
- ✅ 基于OpenAPI规范的类型
- ✅ 编译时类型检查

## API接口列表

### 认证相关

| 方法 | 接口 | 描述 |
|------|------|------|
| `api.sendEmailCode(email)` | POST /api/v1/auth/email-code | 发送邮箱验证码 |
| `api.emailLogin(email, code)` | POST /api/v1/auth/email-login | 邮箱验证码登录 |
| `api.login(email, password)` | POST /api/v1/auth/login | 用户登录 |
| `api.recover(email)` | POST /api/v1/auth/recover | 密码恢复 |
| `api.refreshToken()` | POST /api/v1/auth/refresh | 刷新访问令牌 |
| `api.register(email, password)` | POST /api/v1/auth/register | 用户注册 |
| `api.updatePassword(password)` | POST /api/v1/auth/update-password | 更新密码 |
| `api.verify(email, token, type)` | POST /api/v1/auth/verify | 验证令牌 |

### 业务相关

| 方法 | 接口 | 描述 |
|------|------|------|
| `api.getAppVersion()` | GET /api/v1/version | 获取应用版本信息 |
| `api.getWalletBalance()` | GET /api/v1/wallet/balance | 查询钱包余额 |
| `api.submitRechargeRequest(amount)` | POST /api/v1/recharge/request | 提交充值申请 |
| `api.getTOSCredentials()` | GET /api/v1/tos/credentials | 获取TOS临时凭证 |
| `api.analyzeImage(image, criteria)` | POST /api/v2/chat/analysis | AI图像分析 |

### 工具方法

| 方法 | 描述 |
|------|------|
| `api.isAuthenticated()` | 检查认证状态 |
| `api.getAccessToken()` | 获取访问令牌 |
| `api.checkConnection()` | 检查API连接 |
| `api.healthCheck()` | 健康检查 |
| `api.getAuthStatus()` | 获取认证状态详情 |

## 配置

### 环境变量

在 `.env` 文件中配置API基础URL：

```env
VITE_API_BASE_URL=https://api.example.com
```

### 自定义配置

```typescript
import { apiClient } from '@/services/api'

// 更新配置
apiClient.updateConfig({
  baseURL: 'https://new-api.example.com',
  timeout: 60000,
  retries: 5
})
```

## 错误处理

### 错误类型

- `ApiError`: API响应错误
- `NetworkError`: 网络连接错误
- `AuthenticationError`: 认证失败
- `ValidationError`: 参数验证错误

### 错误处理示例

```typescript
try {
  await api.login(email, password)
} catch (error) {
  if (error instanceof AuthenticationError) {
    // 处理认证错误
    console.error('登录失败:', error.message)
  } else if (error instanceof NetworkError) {
    // 处理网络错误
    console.error('网络错误:', error.message)
  } else {
    // 处理其他错误
    console.error('未知错误:', error.message)
  }
}
```

## 高级用法

### 自定义拦截器

```typescript
import { apiClient } from '@/services/api'

// 添加请求拦截器
apiClient.addRequestInterceptor({
  onRequest: async (config) => {
    // 添加自定义头部
    return {
      ...config,
      headers: {
        ...config.headers,
        'X-Custom-Header': 'value'
      }
    }
  }
})

// 添加响应拦截器
apiClient.addResponseInterceptor({
  onSuccess: async (response) => {
    console.log('请求成功:', response)
    return response
  },
  onError: async (error) => {
    console.error('请求失败:', error)
  }
})
```

### Vue组合式函数

```typescript
import { ref } from 'vue'
import api from '@/services/api'

export function useAuth() {
  const isLoading = ref(false)
  const user = ref(null)

  const login = async (email: string, password: string) => {
    isLoading.value = true
    try {
      const response = await api.login(email, password)
      user.value = response.user
      return { success: true }
    } catch (error) {
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    await api.logout()
    user.value = null
  }

  return {
    isLoading,
    user,
    login,
    logout,
    isAuthenticated: () => api.isAuthenticated()
  }
}
```

## 最佳实践

### 1. 错误处理

- 始终使用try-catch包装API调用
- 根据错误类型进行不同的处理
- 提供用户友好的错误提示

### 2. 加载状态

- 在API调用期间显示加载状态
- 防止重复提交

### 3. 缓存策略

- 对不经常变化的数据进行缓存
- 合理设置缓存过期时间

### 4. 性能优化

- 使用并行请求减少等待时间
- 避免不必要的API调用
- 使用防抖和节流优化用户交互

## 注意事项

1. **Token管理**: Token会自动存储在localStorage中，应用重启后会自动恢复认证状态
2. **自动刷新**: 当Token即将过期时会自动刷新，无需手动处理
3. **错误重试**: 网络错误会自动重试，认证错误和验证错误不会重试
4. **类型安全**: 所有API调用都有完整的TypeScript类型支持
5. **环境配置**: 不同环境可以使用不同的API配置

## 故障排除

### 常见问题

1. **401错误**: 检查Token是否有效，尝试重新登录
2. **网络错误**: 检查网络连接和API服务器状态
3. **超时错误**: 增加timeout配置或检查网络速度
4. **类型错误**: 确保传入的参数类型正确

### 调试技巧

1. 开启网络面板查看请求详情
2. 检查控制台的错误日志
3. 使用健康检查接口测试连接
4. 查看Token的过期时间和状态
