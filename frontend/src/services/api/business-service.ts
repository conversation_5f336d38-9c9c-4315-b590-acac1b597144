/**
 * 业务服务
 * 提供应用业务相关的API调用方法
 */

import { HttpClient } from './http-client'
import type {
  AppVersionConfig,
  WalletBalance,
  RechargeRequest,
  RechargeResponse,
  TOSCredentials,
  AnalysisRequest,
  AnalysisResponse,
  RequestConfig
} from './types'

export class BusinessService {
  private httpClient: HttpClient

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient
  }

  /**
   * 获取应用版本信息
   */
  async getAppVersion(config?: RequestConfig): Promise<AppVersionConfig> {
    return this.httpClient.get<AppVersionConfig>('/api/v1/version', config)
  }

  /**
   * 查询钱包余额
   */
  async getWalletBalance(config?: RequestConfig): Promise<WalletBalance> {
    return this.httpClient.get<WalletBalance>('/api/v1/wallet/balance', config)
  }

  /**
   * 提交充值申请
   */
  async submitRechargeRequest(request: RechargeRequest, config?: RequestConfig): Promise<RechargeResponse> {
    return this.httpClient.post<RechargeResponse>('/api/v1/recharge/request', request, config)
  }

  /**
   * 获取TOS临时凭证
   */
  async getTOSCredentials(config?: RequestConfig): Promise<TOSCredentials> {
    return this.httpClient.get<TOSCredentials>('/api/v1/tos/credentials', config)
  }

  /**
   * AI图像分析
   */
  async analyzeImage(request: AnalysisRequest, config?: RequestConfig): Promise<AnalysisResponse> {
    return this.httpClient.post<AnalysisResponse>('/api/v2/chat/analysis', request, config)
  }

  /**
   * 查询充值申请列表
   */
  async getRechargeList(config?: RequestConfig): Promise<RechargeResponse[]> {
    return this.httpClient.get<RechargeResponse[]>('/api/v1/recharge/list', config)
  }

  /**
   * 查询交易记录
   */
  async getTransactions(config?: RequestConfig): Promise<any[]> {
    return this.httpClient.get<any[]>('/api/v1/wallet/transactions', config)
  }

  /**
   * 受保护的API测试
   */
  async testProtectedAPI(config?: RequestConfig): Promise<any> {
    return this.httpClient.get<any>('/api/v1/protected', config)
  }
}

/**
 * 管理员服务
 * 提供管理员相关的API调用方法
 */
export class AdminService {
  private httpClient: HttpClient

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient
  }

  /**
   * 获取用户列表
   */
  async getUserList(config?: RequestConfig): Promise<any[]> {
    return this.httpClient.get<any[]>('/api/v1/admin/user/list', config)
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(userId: string, config?: RequestConfig): Promise<any> {
    return this.httpClient.get<any>(`/api/v1/admin/user/info?user_id=${userId}`, config)
  }

  /**
   * 创建用户账户
   */
  async createUser(userData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/user/create', userData, config)
  }

  /**
   * 调整用户余额
   */
  async adjustUserBalance(adjustData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/user/balance/adjust', adjustData, config)
  }

  /**
   * 获取充值申请列表
   */
  async getRechargeRequests(config?: RequestConfig): Promise<any[]> {
    return this.httpClient.get<any[]>('/api/v1/admin/recharge/requests', config)
  }

  /**
   * 处理充值申请
   */
  async processRechargeRequest(processData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/recharge/process', processData, config)
  }

  /**
   * 新增支出记录
   */
  async addExpense(expenseData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/finance/expense', expenseData, config)
  }

  /**
   * 列出支出记录
   */
  async getExpenses(config?: RequestConfig): Promise<any[]> {
    return this.httpClient.get<any[]>('/api/v1/admin/finance/expenses', config)
  }

  /**
   * 按分类获取支出分析
   */
  async getExpenseAnalysis(analysisData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/finance/analysis', analysisData, config)
  }

  /**
   * 获取综合财务报表
   */
  async getFinancialReport(reportData: any, config?: RequestConfig): Promise<any> {
    return this.httpClient.post<any>('/api/v1/admin/finance/report', reportData, config)
  }
}
