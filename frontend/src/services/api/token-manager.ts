/**
 * Token 管理器
 * 负责访问令牌和刷新令牌的存储、获取、验证和自动刷新
 */

import type { TokenInfo, TokenManager } from './types'

export class ApiTokenManager implements TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'app_access_token'
  private static readonly REFRESH_TOKEN_KEY = 'app_refresh_token'
  private static readonly EXPIRES_AT_KEY = 'app_token_expires_at'
  private static readonly EXPIRES_IN_KEY = 'app_token_expires_in'
  
  // 提前5分钟刷新token
  private static readonly REFRESH_THRESHOLD = 5 * 60 * 1000

  /**
   * 获取访问令牌
   */
  getAccessToken(): string | null {
    return localStorage.getItem(ApiTokenManager.ACCESS_TOKEN_KEY)
  }

  /**
   * 获取刷新令牌
   */
  getRefreshToken(): string | null {
    return localStorage.getItem(ApiTokenManager.REFRESH_TOKEN_KEY)
  }

  /**
   * 设置令牌信息
   */
  setTokens(tokens: TokenInfo): void {
    localStorage.setItem(ApiTokenManager.ACCESS_TOKEN_KEY, tokens.accessToken)
    localStorage.setItem(ApiTokenManager.REFRESH_TOKEN_KEY, tokens.refreshToken)
    localStorage.setItem(ApiTokenManager.EXPIRES_AT_KEY, tokens.expiresAt.toString())
    localStorage.setItem(ApiTokenManager.EXPIRES_IN_KEY, tokens.expiresIn.toString())
  }

  /**
   * 清除所有令牌
   */
  clearTokens(): void {
    localStorage.removeItem(ApiTokenManager.ACCESS_TOKEN_KEY)
    localStorage.removeItem(ApiTokenManager.REFRESH_TOKEN_KEY)
    localStorage.removeItem(ApiTokenManager.EXPIRES_AT_KEY)
    localStorage.removeItem(ApiTokenManager.EXPIRES_IN_KEY)
  }

  /**
   * 检查访问令牌是否已过期
   */
  isTokenExpired(): boolean {
    const expiresAt = this.getExpiresAt()
    if (!expiresAt) return true
    
    return Date.now() >= expiresAt * 1000
  }

  /**
   * 检查是否应该刷新令牌
   */
  shouldRefreshToken(): boolean {
    const expiresAt = this.getExpiresAt()
    if (!expiresAt) return false
    
    const refreshTime = expiresAt * 1000 - ApiTokenManager.REFRESH_THRESHOLD
    return Date.now() >= refreshTime
  }

  /**
   * 获取令牌过期时间戳
   */
  getExpiresAt(): number | null {
    const expiresAt = localStorage.getItem(ApiTokenManager.EXPIRES_AT_KEY)
    return expiresAt ? parseInt(expiresAt, 10) : null
  }

  /**
   * 获取令牌有效期（秒）
   */
  getExpiresIn(): number | null {
    const expiresIn = localStorage.getItem(ApiTokenManager.EXPIRES_IN_KEY)
    return expiresIn ? parseInt(expiresIn, 10) : null
  }

  /**
   * 获取令牌剩余有效时间（毫秒）
   */
  getRemainingTime(): number {
    const expiresAt = this.getExpiresAt()
    if (!expiresAt) return 0
    
    return Math.max(0, expiresAt * 1000 - Date.now())
  }

  /**
   * 检查是否有有效的令牌
   */
  hasValidToken(): boolean {
    const accessToken = this.getAccessToken()
    return !!(accessToken && !this.isTokenExpired())
  }

  /**
   * 获取完整的令牌信息
   */
  getTokenInfo(): TokenInfo | null {
    const accessToken = this.getAccessToken()
    const refreshToken = this.getRefreshToken()
    const expiresAt = this.getExpiresAt()
    const expiresIn = this.getExpiresIn()

    if (!accessToken || !refreshToken || !expiresAt || !expiresIn) {
      return null
    }

    return {
      accessToken,
      refreshToken,
      expiresAt,
      expiresIn
    }
  }

  /**
   * 更新访问令牌（保持刷新令牌不变）
   */
  updateAccessToken(accessToken: string, expiresAt: number, expiresIn: number): void {
    localStorage.setItem(ApiTokenManager.ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(ApiTokenManager.EXPIRES_AT_KEY, expiresAt.toString())
    localStorage.setItem(ApiTokenManager.EXPIRES_IN_KEY, expiresIn.toString())
  }

  /**
   * 从响应中提取并设置令牌
   */
  setTokensFromResponse(response: any): void {
    if (response.access_token && response.refresh_token) {
      this.setTokens({
        accessToken: response.access_token,
        refreshToken: response.refresh_token,
        expiresAt: response.expires_at,
        expiresIn: response.expires_in
      })
    }
  }

  /**
   * 从响应中更新访问令牌
   */
  updateTokensFromResponse(response: any): void {
    if (response.access_token) {
      this.updateAccessToken(
        response.access_token,
        response.expires_at,
        response.expires_in
      )
    }
  }
}

// 创建全局单例
export const tokenManager = new ApiTokenManager()
