/**
 * API 使用示例
 * 展示如何使用各种API服务
 */

import api, { apiClient, authService, businessService } from './index'
import type { LoginCredentials } from './types'

/**
 * 认证相关示例
 */
export class AuthExamples {
  /**
   * 完整的登录流程示例
   */
  static async loginFlow(email: string, password: string): Promise<void> {
    try {
      // 方式1: 使用便捷API
      const response = await api.login(email, password)
      console.log('登录成功:', response)
      
      // 方式2: 使用服务实例
      // const response = await authService.login({ email, password })
      
      // 检查认证状态
      const isAuth = api.isAuthenticated()
      console.log('认证状态:', isAuth)
      
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  /**
   * 邮箱验证码登录示例
   */
  static async emailLoginFlow(email: string): Promise<void> {
    try {
      // 1. 发送验证码
      await api.sendEmailCode(email)
      console.log('验证码已发送')
      
      // 2. 用户输入验证码后登录
      const code = '123456' // 实际应用中从用户输入获取
      const response = await api.emailLogin(email, code)
      console.log('邮箱登录成功:', response)
      
    } catch (error) {
      console.error('邮箱登录失败:', error)
      throw error
    }
  }

  /**
   * 密码重置流程示例
   */
  static async passwordResetFlow(email: string): Promise<void> {
    try {
      // 1. 发送重置邮件
      await api.recover(email)
      console.log('重置邮件已发送')
      
      // 2. 验证重置令牌
      const token = 'reset-token' // 从邮件链接获取
      const verifyResponse = await api.verify(email, token, 'recovery')
      console.log('令牌验证成功:', verifyResponse)
      
      // 3. 更新密码
      const newPassword = 'newPassword123'
      await api.updatePassword(newPassword)
      console.log('密码更新成功')
      
    } catch (error) {
      console.error('密码重置失败:', error)
      throw error
    }
  }

  /**
   * 自动令牌刷新示例
   */
  static async tokenRefreshExample(): Promise<void> {
    try {
      // 检查是否需要刷新令牌
      const authStatus = api.getAuthStatus()
      console.log('认证状态:', authStatus)
      
      if (authStatus.shouldRefresh) {
        console.log('正在刷新令牌...')
        await api.refreshToken()
        console.log('令牌刷新成功')
      }
      
    } catch (error) {
      console.error('令牌刷新失败:', error)
      // 刷新失败，需要重新登录
      await api.logout()
      throw error
    }
  }
}

/**
 * 业务功能示例
 */
export class BusinessExamples {
  /**
   * 获取应用信息示例
   */
  static async getAppInfoExample(): Promise<void> {
    try {
      // 获取版本信息
      const version = await api.getAppVersion()
      console.log('应用版本:', version)
      
      // 检查连接状态
      const isConnected = await api.checkConnection()
      console.log('连接状态:', isConnected)
      
      // 健康检查
      const health = await api.healthCheck()
      console.log('健康状态:', health)
      
    } catch (error) {
      console.error('获取应用信息失败:', error)
    }
  }

  /**
   * 钱包相关示例
   */
  static async walletExample(): Promise<void> {
    try {
      // 查询余额
      const balance = await api.getWalletBalance()
      console.log('钱包余额:', balance)
      
      // 查询交易记录
      const transactions = await api.getTransactions()
      console.log('交易记录:', transactions)
      
      // 提交充值申请
      const rechargeResponse = await api.submitRechargeRequest(100, 'alipay')
      console.log('充值申请:', rechargeResponse)
      
      // 查询充值列表
      const rechargeList = await api.getRechargeList()
      console.log('充值列表:', rechargeList)
      
    } catch (error) {
      console.error('钱包操作失败:', error)
    }
  }

  /**
   * AI分析示例
   */
  static async aiAnalysisExample(): Promise<void> {
    try {
      const imageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='
      
      const result = await api.analyzeImage(
        imageBase64,
        '评分标准：满分10分',
        '数学',
        'standard'
      )
      
      console.log('AI分析结果:', result)
      
    } catch (error) {
      console.error('AI分析失败:', error)
    }
  }

  /**
   * TOS凭证示例
   */
  static async tosCredentialsExample(): Promise<void> {
    try {
      const credentials = await api.getTOSCredentials()
      console.log('TOS凭证:', credentials)
      
      // 使用凭证上传文件的示例逻辑
      // 这里只是展示如何获取凭证，实际上传需要使用TOS SDK
      
    } catch (error) {
      console.error('获取TOS凭证失败:', error)
    }
  }
}

/**
 * 错误处理示例
 */
export class ErrorHandlingExamples {
  /**
   * 统一错误处理示例
   */
  static async errorHandlingExample(): Promise<void> {
    try {
      await api.login('<EMAIL>', 'wrongpassword')
    } catch (error: any) {
      // 根据错误类型进行不同处理
      switch (error.name) {
        case 'AuthenticationError':
          console.error('认证失败:', error.message)
          // 跳转到登录页面
          break
          
        case 'ValidationError':
          console.error('参数错误:', error.message)
          // 显示表单验证错误
          break
          
        case 'NetworkError':
          console.error('网络错误:', error.message)
          // 显示网络错误提示
          break
          
        default:
          console.error('未知错误:', error.message)
          // 显示通用错误提示
      }
    }
  }

  /**
   * 重试机制示例
   */
  static async retryExample(): Promise<void> {
    try {
      // 使用自定义重试配置
      const response = await businessService.getAppVersion({
        retries: 5,
        retryDelay: 2000,
        timeout: 10000
      })
      console.log('获取版本成功:', response)
      
    } catch (error) {
      console.error('重试后仍然失败:', error)
    }
  }
}

/**
 * 高级用法示例
 */
export class AdvancedExamples {
  /**
   * 自定义拦截器示例
   */
  static setupCustomInterceptors(): void {
    // 添加请求拦截器
    apiClient.addRequestInterceptor({
      onRequest: async (config) => {
        console.log('发送请求:', config)
        
        // 添加自定义头部
        return {
          ...config,
          headers: {
            ...config.headers,
            'X-Custom-Header': 'custom-value'
          }
        }
      },
      onError: async (error) => {
        console.error('请求错误:', error)
      }
    })

    // 添加响应拦截器
    apiClient.addResponseInterceptor({
      onSuccess: async (response) => {
        console.log('收到响应:', response)
        return response
      },
      onError: async (error) => {
        console.error('响应错误:', error)
        
        // 可以在这里添加全局错误处理逻辑
        if (error.status === 401) {
          // 自动跳转到登录页面
          window.location.href = '/login'
        }
      }
    })
  }

  /**
   * 批量请求示例
   */
  static async batchRequestExample(): Promise<void> {
    try {
      // 并行执行多个请求
      const [version, balance, credentials] = await Promise.all([
        api.getAppVersion(),
        api.getWalletBalance(),
        api.getTOSCredentials()
      ])
      
      console.log('批量请求结果:', { version, balance, credentials })
      
    } catch (error) {
      console.error('批量请求失败:', error)
    }
  }

  /**
   * 配置更新示例
   */
  static configUpdateExample(): void {
    // 更新API配置
    apiClient.updateConfig({
      baseURL: 'https://new-api.example.com',
      timeout: 60000,
      defaultHeaders: {
        'X-New-Header': 'new-value'
      }
    })
    
    console.log('配置已更新:', apiClient.getConfig())
  }
}

/**
 * Vue组合式函数示例
 */
export function useAuth() {
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await api.login(credentials.username, credentials.password)
      return { success: true, data: response }
    } catch (error) {
      return { success: false, error }
    }
  }

  const logout = async () => {
    await api.logout()
  }

  const isAuthenticated = () => {
    return api.isAuthenticated()
  }

  return {
    login,
    logout,
    isAuthenticated
  }
}
