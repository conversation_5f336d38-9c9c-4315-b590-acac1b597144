/**
 * 认证服务
 * 提供用户认证相关的API调用方法
 */

import { HttpClient } from './http-client'
import { tokenManager } from './token-manager'
import type {
  EmailCodeRequest,
  EmailCodeResponse,
  EmailLoginRequest,
  LoginRequest,
  LoginResponse,
  RecoverRequest,
  RecoverResponse,
  RegisterRequest,
  RegisterResponse,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  VerifyRequest,
  VerifyResponse,
  RequestConfig
} from './types'

export class AuthService {
  private httpClient: HttpClient

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient
  }

  /**
   * 发送邮箱验证码
   */
  async sendEmailCode(request: EmailCodeRequest, config?: RequestConfig): Promise<EmailCodeResponse> {
    return this.httpClient.post<EmailCodeResponse>('/api/v1/auth/email-code', request, config)
  }

  /**
   * 邮箱验证码登录
   */
  async emailLogin(request: EmailLoginRequest, config?: RequestConfig): Promise<LoginResponse> {
    const response = await this.httpClient.post<LoginResponse>('/api/v1/auth/email-login', request, config)
    
    // 保存令牌
    if (response.access_token && response.refresh_token) {
      tokenManager.setTokensFromResponse(response)
    }
    
    return response
  }

  /**
   * 用户登录
   */
  async login(request: LoginRequest, config?: RequestConfig): Promise<LoginResponse> {
    const response = await this.httpClient.post<LoginResponse>('/api/v1/auth/login', request, config)
    
    // 保存令牌
    if (response.access_token && response.refresh_token) {
      tokenManager.setTokensFromResponse(response)
    }
    
    return response
  }

  /**
   * 密码恢复
   */
  async recover(request: RecoverRequest, config?: RequestConfig): Promise<RecoverResponse> {
    return this.httpClient.post<RecoverResponse>('/api/v1/auth/recover', request, config)
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(config?: RequestConfig): Promise<LoginResponse> {
    const refreshToken = tokenManager.getRefreshToken()
    if (!refreshToken) {
      throw new Error('没有可用的刷新令牌')
    }

    // 在请求头中添加刷新令牌
    const requestConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'X-Refresh-Token': refreshToken
      }
    }

    const response = await this.httpClient.post<LoginResponse>('/api/v1/auth/refresh', undefined, requestConfig)
    
    // 更新令牌
    if (response.access_token) {
      tokenManager.updateTokensFromResponse(response)
    }
    
    return response
  }

  /**
   * 用户注册
   */
  async register(request: RegisterRequest, config?: RequestConfig): Promise<RegisterResponse> {
    return this.httpClient.post<RegisterResponse>('/api/v1/auth/register', request, config)
  }

  /**
   * 更新密码
   */
  async updatePassword(request: UpdatePasswordRequest, config?: RequestConfig): Promise<UpdatePasswordResponse> {
    return this.httpClient.post<UpdatePasswordResponse>('/api/v1/auth/update-password', request, config)
  }

  /**
   * 验证令牌
   */
  async verify(request: VerifyRequest, config?: RequestConfig): Promise<VerifyResponse> {
    const response = await this.httpClient.post<VerifyResponse>('/api/v1/auth/verify', request, config)
    
    // 如果验证成功并返回了新令牌，保存它们
    if (response.access_token && response.refresh_token) {
      tokenManager.setTokensFromResponse(response)
    }
    
    return response
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    // 清除本地令牌
    tokenManager.clearTokens()
    
    // 这里可以添加调用后端登出API的逻辑
    // await this.httpClient.post('/api/v1/auth/logout')
  }

  /**
   * 检查认证状态
   */
  isAuthenticated(): boolean {
    return tokenManager.hasValidToken()
  }

  /**
   * 获取当前访问令牌
   */
  getAccessToken(): string | null {
    return tokenManager.getAccessToken()
  }

  /**
   * 检查是否需要刷新令牌
   */
  shouldRefreshToken(): boolean {
    return tokenManager.shouldRefreshToken()
  }

  /**
   * 自动刷新令牌（如果需要）
   */
  async autoRefreshToken(): Promise<boolean> {
    if (!this.shouldRefreshToken()) {
      return false
    }

    try {
      await this.refreshToken()
      return true
    } catch (error) {
      console.error('自动刷新令牌失败:', error)
      // 刷新失败，清除令牌
      tokenManager.clearTokens()
      return false
    }
  }

  /**
   * 获取认证头
   */
  getAuthHeaders(): Record<string, string> {
    const token = this.getAccessToken()
    return token ? { 'X-Access-Token': token } : {}
  }
}
