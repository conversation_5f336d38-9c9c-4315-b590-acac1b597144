/**
 * API 客户端主类
 * 整合所有服务，提供统一的API调用入口
 */

import { HttpClient } from './http-client'
import { AuthService } from './auth-service'
import { BusinessService, AdminService } from './business-service'
import { tokenManager } from './token-manager'
import type { ApiClientConfig, RequestInterceptor, ResponseInterceptor } from './types'

export class ApiClient {
  private httpClient: HttpClient
  public auth: AuthService
  public business: BusinessService
  public admin: AdminService

  constructor(config: Partial<ApiClientConfig> = {}) {
    // 创建HTTP客户端
    this.httpClient = new HttpClient({
      baseURL: config.baseURL || import.meta.env.VITE_API_BASE_URL || '',
      timeout: config.timeout || 30000,
      retries: config.retries || 3,
      retryDelay: config.retryDelay || 1000,
      ...config
    })

    // 设置请求拦截器
    this.setupRequestInterceptor()
    
    // 设置响应拦截器
    this.setupResponseInterceptor()

    // 初始化服务
    this.auth = new AuthService(this.httpClient)
    this.business = new BusinessService(this.httpClient)
    this.admin = new AdminService(this.httpClient)
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    const requestInterceptor: RequestInterceptor = {
      onRequest: async (config) => {
        // 自动添加认证头
        const authHeaders = this.auth.getAuthHeaders()
        
        // 检查是否需要刷新令牌
        if (this.auth.shouldRefreshToken()) {
          try {
            await this.auth.autoRefreshToken()
            // 刷新后重新获取认证头
            const newAuthHeaders = this.auth.getAuthHeaders()
            Object.assign(authHeaders, newAuthHeaders)
          } catch (error) {
            console.error('自动刷新令牌失败:', error)
          }
        }

        return {
          ...config,
          headers: {
            ...config.headers,
            ...authHeaders
          }
        }
      },
      onError: async (error) => {
        console.error('请求拦截器错误:', error)
      }
    }

    this.httpClient.addRequestInterceptor(requestInterceptor)
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    const responseInterceptor: ResponseInterceptor = {
      onSuccess: async (response) => {
        // 检查响应中是否包含新的令牌信息
        if (response && typeof response === 'object') {
          if ('access_token' in response && 'refresh_token' in response) {
            tokenManager.setTokensFromResponse(response)
          } else if ('access_token' in response) {
            tokenManager.updateTokensFromResponse(response)
          }
        }
        
        return response
      },
      onError: async (error) => {
        // 处理认证错误
        if (error.status === 401) {
          console.warn('认证失败，清除本地令牌')
          tokenManager.clearTokens()
          
          // 可以在这里触发重新登录逻辑
          // 例如：跳转到登录页面
          if (typeof window !== 'undefined') {
            const event = new CustomEvent('auth:unauthorized', { detail: error })
            window.dispatchEvent(event)
          }
        }
        
        console.error('API响应错误:', error)
      }
    }

    this.httpClient.addResponseInterceptor(responseInterceptor)
  }

  /**
   * 更新API配置
   */
  updateConfig(config: Partial<ApiClientConfig>): void {
    this.httpClient.updateConfig(config)
  }

  /**
   * 获取当前配置
   */
  getConfig(): ApiClientConfig {
    return this.httpClient.getConfig()
  }

  /**
   * 添加自定义请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.httpClient.addRequestInterceptor(interceptor)
  }

  /**
   * 添加自定义响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.httpClient.addResponseInterceptor(interceptor)
  }

  /**
   * 检查API连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.business.getAppVersion({ timeout: 5000 })
      return true
    } catch (error) {
      console.error('API连接检查失败:', error)
      return false
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy'
    timestamp: number
    details?: any
  }> {
    const timestamp = Date.now()
    
    try {
      const version = await this.business.getAppVersion({ timeout: 5000 })
      return {
        status: 'healthy',
        timestamp,
        details: { version: version.version }
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp,
        details: { error: error instanceof Error ? error.message : '未知错误' }
      }
    }
  }

  /**
   * 获取认证状态
   */
  getAuthStatus(): {
    isAuthenticated: boolean
    hasValidToken: boolean
    shouldRefresh: boolean
    tokenInfo: any
  } {
    return {
      isAuthenticated: this.auth.isAuthenticated(),
      hasValidToken: tokenManager.hasValidToken(),
      shouldRefresh: tokenManager.shouldRefreshToken(),
      tokenInfo: tokenManager.getTokenInfo()
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 清理令牌
    tokenManager.clearTokens()
    
    // 这里可以添加其他清理逻辑
    console.log('API客户端已销毁')
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient()

// 导出服务实例以便直接使用
export const authService = apiClient.auth
export const businessService = apiClient.business
export const adminService = apiClient.admin

// 导出类型
export * from './types'
export { tokenManager } from './token-manager'
