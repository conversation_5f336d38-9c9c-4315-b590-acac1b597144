/**
 * HTTP 客户端基础类
 * 提供统一的HTTP请求处理、错误处理、重试机制等功能
 */

import type {
  ApiClientConfig,
  RequestConfig,
  ApiError,
  NetworkError,
  AuthenticationError,
  ValidationError,
  ResponseInterceptor,
  RequestInterceptor
} from './types'

export class HttpClient {
  private config: ApiClientConfig
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = {
      baseURL: config.baseURL || '',
      timeout: config.timeout || 30000,
      retries: config.retries || 3,
      retryDelay: config.retryDelay || 1000,
      defaultHeaders: {
        'Content-Type': 'application/json',
        ...config.defaultHeaders
      }
    }
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 执行HTTP请求
   */
  async request<T = any>(
    method: string,
    url: string,
    data?: any,
    config: RequestConfig = {}
  ): Promise<T> {
    const requestConfig = await this.prepareRequest(method, url, data, config)
    
    let lastError: Error
    const maxRetries = config.retries ?? this.config.retries

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.executeRequest<T>(requestConfig)
        return await this.processResponse(response)
      } catch (error) {
        lastError = error as Error
        
        // 如果是认证错误或验证错误，不重试
        if (error instanceof AuthenticationError || error instanceof ValidationError) {
          throw error
        }
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === maxRetries) {
          break
        }
        
        // 等待后重试
        await this.delay(config.retryDelay ?? this.config.retryDelay)
      }
    }

    throw lastError!
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>('GET', url, undefined, config)
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>('POST', url, data, config)
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>('PUT', url, data, config)
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>('DELETE', url, undefined, config)
  }

  /**
   * 准备请求配置
   */
  private async prepareRequest(
    method: string,
    url: string,
    data?: any,
    config: RequestConfig = {}
  ): Promise<RequestInit & { url: string }> {
    let requestConfig: RequestConfig = {
      ...config,
      headers: {
        ...this.config.defaultHeaders,
        ...config.headers
      }
    }

    // 执行请求拦截器
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onRequest) {
        try {
          requestConfig = await interceptor.onRequest(requestConfig)
        } catch (error) {
          if (interceptor.onError) {
            await interceptor.onError(error as Error)
          }
          throw error
        }
      }
    }

    const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`

    return {
      url: fullUrl,
      method: method.toUpperCase(),
      headers: requestConfig.headers,
      body: data ? JSON.stringify(data) : undefined,
      signal: this.createAbortSignal(config.timeout ?? this.config.timeout)
    }
  }

  /**
   * 执行实际的HTTP请求
   */
  private async executeRequest<T>(config: RequestInit & { url: string }): Promise<T> {
    try {
      const response = await fetch(config.url, config)
      
      if (!response.ok) {
        throw await this.createApiError(response)
      }

      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        return await response.json()
      } else {
        return await response.text() as unknown as T
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      // 网络错误或其他错误
      throw new NetworkError(
        `网络请求失败: ${error instanceof Error ? error.message : '未知错误'}`,
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * 处理响应
   */
  private async processResponse<T>(response: T): Promise<T> {
    let processedResponse = response

    // 执行响应拦截器
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onSuccess) {
        try {
          processedResponse = await interceptor.onSuccess(processedResponse)
        } catch (error) {
          if (interceptor.onError) {
            await interceptor.onError(error as ApiError)
          }
          throw error
        }
      }
    }

    return processedResponse
  }

  /**
   * 创建API错误对象
   */
  private async createApiError(response: Response): Promise<ApiError> {
    let errorData: any = {}
    
    try {
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        errorData = await response.json()
      } else {
        errorData = { message: await response.text() }
      }
    } catch {
      errorData = { message: response.statusText }
    }

    const message = errorData.message || errorData.error || `HTTP ${response.status} 错误`

    switch (response.status) {
      case 401:
        return new AuthenticationError(message)
      case 400:
        return new ValidationError(message)
      default:
        return new ApiError(message, response.status, errorData.code, errorData)
    }
  }

  /**
   * 创建超时信号
   */
  private createAbortSignal(timeout: number): AbortSignal {
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller.signal
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ApiClientConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ApiClientConfig {
    return { ...this.config }
  }
}
