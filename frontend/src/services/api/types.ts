/**
 * API 接口类型定义
 * 基于 OpenAPI 规范生成的类型定义
 */

// ============= 基础类型 =============

export interface ApiResponse<T = any> {
  success?: boolean
  message?: string
  data?: T
}

export interface ErrorResponse {
  error?: string
  message?: string
}

// ============= 认证相关类型 =============

export interface EmailCodeRequest {
  email: string
}

export interface EmailCodeResponse {
  message: string
  success: boolean
}

export interface EmailLoginRequest {
  email: string
  code: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  expires_at: number
  config: ConfigItem[]
  subjects: Subject[]
}

export interface RecoverRequest {
  email: string
}

export interface RecoverResponse {
  message: string
  success: boolean
}

export interface RegisterRequest {
  email: string
  password: string
}

export interface RegisterResponse {
  code: number
  message: string
  success: boolean
}

export interface UpdatePasswordRequest {
  password: string
}

export interface UpdatePasswordResponse {
  message: string
  success: boolean
}

export interface VerifyRequest {
  email: string
  token: string
  type: string
}

export interface VerifyResponse {
  access_token?: string
  refresh_token?: string
  expires_in?: number
  expires_at?: number
  message: string
  success: boolean
}

// ============= 配置相关类型 =============

export interface ConfigItem {
  id: string
  name: string
  url: string
  actions: Action[]
}

export interface Action {
  type: string
  selector: string
  index: number
  value?: string
}

export interface Subject {
  subject_id: string
  subject_name: string
  prompt_text: string
}

// ============= 应用版本类型 =============

export interface AppVersionConfig {
  version: string
  download_url: string
  update_log: string
  force_update: boolean
}

// ============= 钱包相关类型 =============

export interface WalletBalance {
  balance: number
  currency: string
  last_updated: string
}

export interface RechargeRequest {
  amount: number
  payment_method?: string
}

export interface RechargeResponse {
  request_id: string
  amount: number
  status: string
  created_at: string
}

// ============= TOS 凭证类型 =============

export interface TOSCredentials {
  access_key_id: string
  secret_access_key: string
  session_token: string
  region: string
  bucket: string
  expires_at: number
}

// ============= AI 分析类型 =============

export interface AnalysisRequest {
  image: string // base64 编码的图片
  criteria?: string
  subject?: string
  analysis_mode?: string
}

export interface AnalysisResponse {
  student_answer: string
  score: number
  grading_details: string
  analysis_id?: string
  created_at?: string
}

// ============= HTTP 配置类型 =============

export interface RequestConfig {
  timeout?: number
  retries?: number
  retryDelay?: number
  headers?: Record<string, string>
}

export interface ApiClientConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
  defaultHeaders: Record<string, string>
}

// ============= 错误类型 =============

export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR')
    this.name = 'AuthenticationError'
  }
}

export class ValidationError extends ApiError {
  constructor(message: string = '请求参数错误') {
    super(message, 400, 'VALIDATION_ERROR')
    this.name = 'ValidationError'
  }
}

// ============= 响应拦截器类型 =============

export interface ResponseInterceptor {
  onSuccess?: <T>(response: T) => T | Promise<T>
  onError?: (error: ApiError) => void | Promise<void>
}

export interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onError?: (error: Error) => void | Promise<void>
}

// ============= Token 管理类型 =============

export interface TokenInfo {
  accessToken: string
  refreshToken: string
  expiresAt: number
  expiresIn: number
}

export interface TokenManager {
  getAccessToken(): string | null
  getRefreshToken(): string | null
  setTokens(tokens: TokenInfo): void
  clearTokens(): void
  isTokenExpired(): boolean
  shouldRefreshToken(): boolean
}
