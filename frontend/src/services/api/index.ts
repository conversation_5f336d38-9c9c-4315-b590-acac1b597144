/**
 * API 服务入口文件
 * 统一导出所有API相关的类、函数和类型
 */

// 导出主要的API客户端
export {
  ApiClient,
  apiClient,
  authService,
  businessService,
  adminService,
  tokenManager
} from './api-client'

// 导出服务类
export { AuthService } from './auth-service'
export { BusinessService, AdminService } from './business-service'
export { HttpClient } from './http-client'
export { ApiTokenManager } from './token-manager'

// 导出所有类型
export * from './types'

// 导出便捷的API调用方法
import { apiClient } from './api-client'

/**
 * 便捷的API调用方法
 * 提供简化的API调用接口
 */
export const api = {
  // ============= 认证相关 =============
  
  /**
   * 发送邮箱验证码
   */
  sendEmailCode: (email: string) => 
    apiClient.auth.sendEmailCode({ email }),

  /**
   * 邮箱验证码登录
   */
  emailLogin: (email: string, code: string) => 
    apiClient.auth.emailLogin({ email, code }),

  /**
   * 用户登录
   */
  login: (email: string, password: string) => 
    apiClient.auth.login({ email, password }),

  /**
   * 密码恢复
   */
  recover: (email: string) => 
    apiClient.auth.recover({ email }),

  /**
   * 刷新令牌
   */
  refreshToken: () => 
    apiClient.auth.refreshToken(),

  /**
   * 用户注册
   */
  register: (email: string, password: string) => 
    apiClient.auth.register({ email, password }),

  /**
   * 更新密码
   */
  updatePassword: (password: string) => 
    apiClient.auth.updatePassword({ password }),

  /**
   * 验证令牌
   */
  verify: (email: string, token: string, type: string) => 
    apiClient.auth.verify({ email, token, type }),

  /**
   * 登出
   */
  logout: () => 
    apiClient.auth.logout(),

  // ============= 业务相关 =============

  /**
   * 获取应用版本信息
   */
  getAppVersion: () => 
    apiClient.business.getAppVersion(),

  /**
   * 查询钱包余额
   */
  getWalletBalance: () => 
    apiClient.business.getWalletBalance(),

  /**
   * 提交充值申请
   */
  submitRechargeRequest: (amount: number, paymentMethod?: string) => 
    apiClient.business.submitRechargeRequest({ amount, payment_method: paymentMethod }),

  /**
   * 获取TOS临时凭证
   */
  getTOSCredentials: () => 
    apiClient.business.getTOSCredentials(),

  /**
   * AI图像分析
   */
  analyzeImage: (image: string, criteria?: string, subject?: string, analysisMode?: string) => 
    apiClient.business.analyzeImage({ 
      image, 
      criteria, 
      subject, 
      analysis_mode: analysisMode 
    }),

  /**
   * 查询充值申请列表
   */
  getRechargeList: () => 
    apiClient.business.getRechargeList(),

  /**
   * 查询交易记录
   */
  getTransactions: () => 
    apiClient.business.getTransactions(),

  // ============= 工具方法 =============

  /**
   * 检查认证状态
   */
  isAuthenticated: () => 
    apiClient.auth.isAuthenticated(),

  /**
   * 获取访问令牌
   */
  getAccessToken: () => 
    apiClient.auth.getAccessToken(),

  /**
   * 检查API连接
   */
  checkConnection: () => 
    apiClient.checkConnection(),

  /**
   * 健康检查
   */
  healthCheck: () => 
    apiClient.healthCheck(),

  /**
   * 获取认证状态详情
   */
  getAuthStatus: () => 
    apiClient.getAuthStatus(),

  // ============= 管理员相关 =============
  admin: {
    /**
     * 获取用户列表
     */
    getUserList: () => 
      apiClient.admin.getUserList(),

    /**
     * 获取用户信息
     */
    getUserInfo: (userId: string) => 
      apiClient.admin.getUserInfo(userId),

    /**
     * 创建用户
     */
    createUser: (userData: any) => 
      apiClient.admin.createUser(userData),

    /**
     * 调整用户余额
     */
    adjustUserBalance: (adjustData: any) => 
      apiClient.admin.adjustUserBalance(adjustData),

    /**
     * 获取充值申请列表
     */
    getRechargeRequests: () => 
      apiClient.admin.getRechargeRequests(),

    /**
     * 处理充值申请
     */
    processRechargeRequest: (processData: any) => 
      apiClient.admin.processRechargeRequest(processData),

    /**
     * 新增支出记录
     */
    addExpense: (expenseData: any) => 
      apiClient.admin.addExpense(expenseData),

    /**
     * 获取支出记录
     */
    getExpenses: () => 
      apiClient.admin.getExpenses(),

    /**
     * 获取支出分析
     */
    getExpenseAnalysis: (analysisData: any) => 
      apiClient.admin.getExpenseAnalysis(analysisData),

    /**
     * 获取财务报表
     */
    getFinancialReport: (reportData: any) => 
      apiClient.admin.getFinancialReport(reportData)
  }
}

// 默认导出API对象
export default api
