// 用户信息
export interface UserInfo {
  email: string
  name?: string
}

// 登录凭据
export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

// 配置信息
export interface Config {
  apiUrl?: string
  subjects?: string[]
  selectedUrl?: string
}

// API相关类型（从API服务导入）
export type {
  ApiResponse,
  ErrorResponse,
  EmailCodeRequest,
  EmailCodeResponse,
  EmailLoginRequest,
  LoginRequest,
  LoginResponse,
  RecoverRequest,
  RecoverResponse,
  RegisterRequest,
  RegisterResponse,
  UpdatePasswordRequest,
  UpdatePasswordResponse,
  VerifyRequest,
  VerifyResponse,
  AppVersionConfig,
  WalletBalance,
  RechargeRequest,
  RechargeResponse,
  TOSCredentials,
  AnalysisRequest,
  AnalysisResponse,
  ApiError,
  NetworkError,
  AuthenticationError,
  ValidationError
} from '@/services/api'

// 区域坐标
export interface AreaCoords {
  x: number
  y: number
  width: number
  height: number
}

// 配置项
export interface ConfigItem {
  id: string
  name: string
  url: string
  actions: Action[]
}

// 操作配置
export interface Action {
  type: 'click' | 'fill' | 'screenshot'
  selector: string
  index: number
  value?: string
}

// 评分标准
export interface GradingCriteria {
  id: string
  content: string
  defaultCriteria: string
  scoringPoints: string
  deductionPoints: string
  totalScore: number
}

// 阅卷记录
export interface GradingRecord {
  id: number
  userEmail: string
  answerImage: string
  answerText: string
  score: number
  scoreDetails: string
  createdAt: string
}

// 元素操作
export interface ElementOperation {
  type: 'click' | 'input'
  selector: string
  value?: string
  description: string
}
