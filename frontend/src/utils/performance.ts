/**
 * 性能优化工具函数
 */

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 图片懒加载
export function lazyLoadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

// 虚拟滚动计算
export interface VirtualScrollOptions {
  itemHeight: number
  containerHeight: number
  totalItems: number
  overscan?: number
}

export function calculateVirtualScroll(
  scrollTop: number,
  options: VirtualScrollOptions
) {
  const { itemHeight, containerHeight, totalItems, overscan = 5 } = options
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    totalItems - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )
  
  const visibleItems = endIndex - startIndex + 1
  const totalHeight = totalItems * itemHeight
  const offsetY = startIndex * itemHeight
  
  return {
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY
  }
}

// 内存使用监控
export function getMemoryUsage(): PerformanceMemory | null {
  if ('memory' in performance) {
    return (performance as any).memory
  }
  return null
}

// 性能标记
export class PerformanceMarker {
  private marks: Map<string, number> = new Map()
  
  mark(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  measure(name: string, startMark: string, endMark?: string): number {
    const startTime = this.marks.get(startMark)
    if (!startTime) {
      throw new Error(`Start mark "${startMark}" not found`)
    }
    
    const endTime = endMark ? this.marks.get(endMark) : performance.now()
    if (endMark && !endTime) {
      throw new Error(`End mark "${endMark}" not found`)
    }
    
    const duration = (endTime || performance.now()) - startTime
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
    return duration
  }
  
  clear(): void {
    this.marks.clear()
  }
}

// 组件渲染性能监控
export function withPerformanceMonitoring<T extends Record<string, any>>(
  component: T,
  componentName: string
): T {
  const marker = new PerformanceMarker()
  
  return new Proxy(component, {
    get(target, prop) {
      if (prop === 'render' || prop === 'setup') {
        return function (...args: any[]) {
          marker.mark(`${componentName}-${String(prop)}-start`)
          const result = target[prop].apply(this, args)
          marker.mark(`${componentName}-${String(prop)}-end`)
          marker.measure(
            `${componentName}-${String(prop)}`,
            `${componentName}-${String(prop)}-start`,
            `${componentName}-${String(prop)}-end`
          )
          return result
        }
      }
      return target[prop]
    }
  })
}

// 图片压缩
export function compressImage(
  file: File,
  maxWidth: number = 800,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算新尺寸
      const { width, height } = img
      const ratio = Math.min(maxWidth / width, maxWidth / height)
      
      canvas.width = width * ratio
      canvas.height = height * ratio
      
      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
      
      // 转换为 Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        'image/jpeg',
        quality
      )
    }
    
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

// 批量处理
export async function batchProcess<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  batchSize: number = 10,
  delay: number = 0
): Promise<R[]> {
  const results: R[] = []
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map(processor))
    results.push(...batchResults)
    
    // 添加延迟以避免阻塞 UI
    if (delay > 0 && i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  return results
}

// 缓存装饰器
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>()
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = fn(...args)
    cache.set(key, result)
    return result
  }) as T
}

// 资源预加载
export function preloadResources(urls: string[]): Promise<void[]> {
  return Promise.all(
    urls.map(url => {
      if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
        return lazyLoadImage(url).then(() => void 0)
      } else {
        return fetch(url).then(() => void 0)
      }
    })
  )
}

// 创建全局性能监控实例
export const performanceMarker = new PerformanceMarker()
