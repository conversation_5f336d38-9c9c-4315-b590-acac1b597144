import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/LoginView.vue')
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
