import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AreaCoords, GradingRecord, GradingCriteria } from '@/types'
import { debounce, memoize } from '@/utils/performance'

export const useGradingStore = defineStore('grading', () => {
  // 状态
  const isAutoGrading = ref(false)
  const currentImage = ref('')
  const gradingCriteria = ref('')
  const selectedUrl = ref('')
  const selectedAreaCoords = ref<AreaCoords | null>(null)
  const gradingRecords = ref<GradingRecord[]>([])
  const paperCount = ref(1)
  const currentPaper = ref(0)

  // 计算属性
  const hasImage = computed(() => !!currentImage.value)
  const hasAreaSelected = computed(() => !!selectedAreaCoords.value)
  const canStartGrading = computed(() => {
    return !isAutoGrading.value &&
           gradingCriteria.value.trim() !== '' &&
           selectedUrl.value !== '' &&
           (selectedUrl.value !== 'custom' || hasAreaSelected.value)
  })

  // 操作
  const startAutoGrading = async (count: number) => {
    if (isAutoGrading.value) {
      throw new Error('自动阅卷已在进行中')
    }

    if (!canStartGrading.value) {
      throw new Error('请先配置阅卷条件')
    }

    try {
      isAutoGrading.value = true
      paperCount.value = count
      currentPaper.value = 0

      // TODO: 调用后端开始自动阅卷
      // await api.startAutoGrading(count, gradingCriteria.value)
      
      return { success: true }
    } catch (error) {
      isAutoGrading.value = false
      throw new Error(`启动自动阅卷失败: ${error}`)
    }
  }

  const stopAutoGrading = async () => {
    try {
      // TODO: 调用后端停止自动阅卷
      // await api.stopAutoGrading()
      
      isAutoGrading.value = false
      currentPaper.value = 0
      
      return { success: true }
    } catch (error) {
      throw new Error(`停止自动阅卷失败: ${error}`)
    }
  }

  const fetchImage = async () => {
    try {
      let imageData: string

      if (selectedUrl.value === 'custom' && selectedAreaCoords.value) {
        // 使用区域截图
        const { x, y, width, height } = selectedAreaCoords.value
        // TODO: 调用后端区域截图API
        // imageData = await api.screenshotArea(x, y, width, height, 800)
        imageData = 'data:image/png;base64,mock-image-data'
      } else {
        // 使用常规截图
        // TODO: 调用后端截图API
        // imageData = await api.fetchImage(selectedUrl.value)
        imageData = 'data:image/png;base64,mock-image-data'
      }

      currentImage.value = imageData
      return { success: true, data: imageData }
    } catch (error) {
      throw new Error(`抓图失败: ${error}`)
    }
  }

  const submitGrade = async (score: number) => {
    try {
      // TODO: 调用后端提交评分API
      // await api.submitGrade(selectedUrl.value, score.toString())
      
      return { success: true }
    } catch (error) {
      throw new Error(`提交评分失败: ${error}`)
    }
  }

  const loadGradingRecords = async () => {
    try {
      // TODO: 调用后端获取阅卷记录API
      // const records = await api.getGradingRecords()
      // gradingRecords.value = records
      
      gradingRecords.value = [] // 模拟空记录
      return { success: true }
    } catch (error) {
      throw new Error(`加载阅卷记录失败: ${error}`)
    }
  }

  const setGradingCriteria = (criteria: string) => {
    gradingCriteria.value = criteria
  }

  const setSelectedUrl = (url: string) => {
    selectedUrl.value = url
    // 如果不是自定义URL，清除区域选择
    if (url !== 'custom') {
      selectedAreaCoords.value = null
    }
  }

  const setSelectedAreaCoords = (coords: AreaCoords | null) => {
    selectedAreaCoords.value = coords
  }

  const clearCurrentImage = () => {
    currentImage.value = ''
  }

  const updateProgress = (current: number) => {
    currentPaper.value = current
  }

  return {
    // 状态
    isAutoGrading,
    currentImage,
    gradingCriteria,
    selectedUrl,
    selectedAreaCoords,
    gradingRecords,
    paperCount,
    currentPaper,
    
    // 计算属性
    hasImage,
    hasAreaSelected,
    canStartGrading,
    
    // 操作
    startAutoGrading,
    stopAutoGrading,
    fetchImage,
    submitGrade,
    loadGradingRecords,
    setGradingCriteria,
    setSelectedUrl,
    setSelectedAreaCoords,
    clearCurrentImage,
    updateProgress
  }
})
