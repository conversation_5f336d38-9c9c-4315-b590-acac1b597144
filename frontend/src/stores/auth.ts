import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginCredentials, Config } from '@/types'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const token = ref('')
  const config = ref<Config | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value && !!userInfo.value)

  // 操作
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await api.login(credentials.username, credentials.password)

      // 更新状态
      isAuthenticated.value = true
      userInfo.value = {
        email: credentials.username,
        name: credentials.username
      }
      token.value = response.access_token

      // 更新配置信息
      if (response.config) {
        config.value = {
          subjects: response.subjects?.map(s => s.subject_name) || [],
          selectedUrl: config.value?.selectedUrl
        }
      }

      // 如果选择记住登录，额外保存到配置文件（通过app前缀）
      if (credentials.rememberMe) {
        // Token已经通过TokenManager自动保存到localStorage
        // 这里可以保存其他需要记住的信息
        localStorage.setItem('app_remember_login', 'true')
        localStorage.setItem('app_user_email', credentials.username)
      }

      return { success: true, data: response }
    } catch (error) {
      throw new Error(`登录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const logout = async () => {
    try {
      await api.logout()
    } catch (error) {
      console.error('登出API调用失败:', error)
    }

    // 清除状态
    isAuthenticated.value = false
    userInfo.value = null
    token.value = ''
    config.value = null

    // 清除记住登录的信息
    localStorage.removeItem('app_remember_login')
    localStorage.removeItem('app_user_email')
  }

  const refreshToken = async () => {
    try {
      const response = await api.refreshToken()
      token.value = response.access_token
      return { success: true, data: response }
    } catch (error) {
      await logout()
      throw new Error(`刷新token失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const updateConfig = (newConfig: Config) => {
    config.value = { ...config.value, ...newConfig }
  }

  // 新增方法
  const sendEmailCode = async (email: string) => {
    try {
      const response = await api.sendEmailCode(email)
      return { success: true, data: response }
    } catch (error) {
      throw new Error(`发送验证码失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const emailLogin = async (email: string, code: string) => {
    try {
      const response = await api.emailLogin(email, code)

      // 更新状态
      isAuthenticated.value = true
      userInfo.value = {
        email: email,
        name: email
      }
      token.value = response.access_token

      // 更新配置信息
      if (response.config) {
        config.value = {
          subjects: response.subjects?.map(s => s.subject_name) || [],
          selectedUrl: config.value?.selectedUrl
        }
      }

      return { success: true, data: response }
    } catch (error) {
      throw new Error(`邮箱登录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const register = async (email: string, password: string) => {
    try {
      const response = await api.register(email, password)
      return { success: true, data: response }
    } catch (error) {
      throw new Error(`注册失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const recoverPassword = async (email: string) => {
    try {
      const response = await api.recover(email)
      return { success: true, data: response }
    } catch (error) {
      throw new Error(`密码恢复失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const updatePassword = async (password: string) => {
    try {
      const response = await api.updatePassword(password)
      return { success: true, data: response }
    } catch (error) {
      throw new Error(`密码更新失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const verifyToken = async (email: string, token: string, type: string) => {
    try {
      const response = await api.verify(email, token, type)

      // 如果验证成功并返回了新令牌，更新认证状态
      if (response.access_token) {
        isAuthenticated.value = true
        userInfo.value = {
          email: email,
          name: email
        }
        token.value = response.access_token
      }

      return { success: true, data: response }
    } catch (error) {
      throw new Error(`令牌验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  const initializeAuth = () => {
    // 检查是否有有效的认证状态
    const isAuth = api.isAuthenticated()
    const authStatus = api.getAuthStatus()

    if (isAuth && authStatus.tokenInfo) {
      // 恢复认证状态
      isAuthenticated.value = true
      token.value = authStatus.tokenInfo.accessToken

      // 尝试从本地存储恢复用户信息
      const savedEmail = localStorage.getItem('app_user_email')
      if (savedEmail) {
        userInfo.value = {
          email: savedEmail,
          name: savedEmail
        }
      }
    } else {
      // 清除无效状态
      isAuthenticated.value = false
      userInfo.value = null
      token.value = ''
    }
  }

  return {
    // 状态
    isAuthenticated,
    userInfo,
    token,
    config,

    // 计算属性
    isLoggedIn,

    // 操作
    login,
    logout,
    refreshToken,
    updateConfig,
    initializeAuth,
    sendEmailCode,
    emailLogin,
    register,
    recoverPassword,
    updatePassword,
    verifyToken
  }
})
