import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginCredentials, Config } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const token = ref('')
  const config = ref<Config | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => isAuthenticated.value && !!userInfo.value)

  // 操作
  const login = async (credentials: LoginCredentials) => {
    try {
      // TODO: 调用后端登录API
      // const response = await api.login(credentials)
      
      // 模拟登录成功
      isAuthenticated.value = true
      userInfo.value = {
        email: credentials.username,
        name: credentials.username
      }
      token.value = 'mock-token'

      // 如果选择记住登录，保存到本地存储
      if (credentials.rememberMe) {
        localStorage.setItem('auth_token', token.value)
        localStorage.setItem('user_info', JSON.stringify(userInfo.value))
      }

      return { success: true }
    } catch (error) {
      throw new Error(`登录失败: ${error}`)
    }
  }

  const logout = () => {
    isAuthenticated.value = false
    userInfo.value = null
    token.value = ''
    config.value = null

    // 清除本地存储
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
  }

  const refreshToken = async () => {
    try {
      // TODO: 调用后端刷新token API
      // const response = await api.refreshToken()
      // token.value = response.token
      return { success: true }
    } catch (error) {
      logout()
      throw new Error(`刷新token失败: ${error}`)
    }
  }

  const updateConfig = (newConfig: Config) => {
    config.value = { ...config.value, ...newConfig }
  }

  const initializeAuth = () => {
    // 从本地存储恢复登录状态
    const savedToken = localStorage.getItem('auth_token')
    const savedUserInfo = localStorage.getItem('user_info')

    if (savedToken && savedUserInfo) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUserInfo)
        isAuthenticated.value = true
      } catch (error) {
        console.error('Failed to restore auth state:', error)
        logout()
      }
    }
  }

  return {
    // 状态
    isAuthenticated,
    userInfo,
    token,
    config,
    
    // 计算属性
    isLoggedIn,
    
    // 操作
    login,
    logout,
    refreshToken,
    updateConfig,
    initializeAuth
  }
})
