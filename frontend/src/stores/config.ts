import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ConfigItem } from '@/types'

export const useConfigStore = defineStore('config', () => {
  // 状态
  const urlOptions = ref<ConfigItem[]>([])
  const subjects = ref<string[]>(['语文', '数学', '英语', '物理', '化学', '生物', '地理', '历史', '政治'])
  const selectedSubject = ref('')
  const analysisMode = ref('standard')
  const loading = ref(false)

  // 计算属性
  const hasUrlOptions = computed(() => urlOptions.value.length > 0)
  const currentUrlOption = computed(() => {
    return urlOptions.value.find(option => option.id === 'current') || null
  })

  // 操作
  const loadConfig = async () => {
    try {
      loading.value = true
      
      // TODO: 调用后端获取配置API
      // const config = await api.getConfig()
      // urlOptions.value = config.urlOptions || []
      // selectedSubject.value = config.selectedSubject || ''
      // analysisMode.value = config.analysisMode || 'standard'
      
      // 模拟配置数据
      urlOptions.value = [
        {
          id: 'custom',
          name: '自定义URL',
          url: '',
          actions: []
        }
      ]
      
      return { success: true }
    } catch (error) {
      throw new Error(`加载配置失败: ${error}`)
    } finally {
      loading.value = false
    }
  }

  const saveConfig = async () => {
    try {
      loading.value = true
      
      const config = {
        urlOptions: urlOptions.value,
        selectedSubject: selectedSubject.value,
        analysisMode: analysisMode.value
      }
      
      // TODO: 调用后端保存配置API
      // await api.saveConfig(config)
      
      return { success: true }
    } catch (error) {
      throw new Error(`保存配置失败: ${error}`)
    } finally {
      loading.value = false
    }
  }

  const addUrlOption = (option: ConfigItem) => {
    const existingIndex = urlOptions.value.findIndex(item => item.id === option.id)
    if (existingIndex >= 0) {
      urlOptions.value[existingIndex] = option
    } else {
      urlOptions.value.push(option)
    }
  }

  const removeUrlOption = (id: string) => {
    const index = urlOptions.value.findIndex(item => item.id === id)
    if (index >= 0) {
      urlOptions.value.splice(index, 1)
    }
  }

  const setSelectedSubject = (subject: string) => {
    selectedSubject.value = subject
  }

  const setAnalysisMode = (mode: string) => {
    analysisMode.value = mode
  }

  const getUrlOptionById = (id: string) => {
    return urlOptions.value.find(option => option.id === id) || null
  }

  const updateUrlOption = (id: string, updates: Partial<ConfigItem>) => {
    const index = urlOptions.value.findIndex(item => item.id === id)
    if (index >= 0) {
      urlOptions.value[index] = { ...urlOptions.value[index], ...updates }
    }
  }

  return {
    // 状态
    urlOptions,
    subjects,
    selectedSubject,
    analysisMode,
    loading,
    
    // 计算属性
    hasUrlOptions,
    currentUrlOption,
    
    // 操作
    loadConfig,
    saveConfig,
    addUrlOption,
    removeUrlOption,
    setSelectedSubject,
    setAnalysisMode,
    getUrlOptionById,
    updateUrlOption
  }
})
