import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { ElButton, ElInput, ElForm, ElFormItem, ElCheckbox } from 'element-plus'
import LoginView from '@/views/login/LoginView.vue'
import { useAuthStore } from '@/stores/auth'

// Mock router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

describe('Login Integration', () => {
  let wrapper: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    
    wrapper = mount(LoginView, {
      global: {
        components: {
          ElButton,
          ElInput,
          ElForm,
          ElFormItem,
          ElCheckbox
        },
        stubs: {
          'el-icon': true
        }
      }
    })
    
    vi.clearAllMocks()
  })

  it('should render login form correctly', () => {
    expect(wrapper.find('h1').text()).toBe('山竹阅卷')
    expect(wrapper.find('p').text()).toBe('智能阅卷系统')
    expect(wrapper.find('input[placeholder="用户名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="密码"]').exists()).toBe(true)
    expect(wrapper.find('.el-checkbox').exists()).toBe(true)
    expect(wrapper.find('button').text()).toContain('登录')
  })

  it('should validate required fields', async () => {
    const loginButton = wrapper.find('button')
    
    await loginButton.trigger('click')
    
    // Should show validation errors for empty fields
    expect(wrapper.text()).toContain('请输入用户名')
    expect(wrapper.text()).toContain('请输入密码')
  })

  it('should validate password length', async () => {
    const usernameInput = wrapper.find('input[placeholder="用户名"]')
    const passwordInput = wrapper.find('input[placeholder="密码"]')
    const loginButton = wrapper.find('button')
    
    await usernameInput.setValue('<EMAIL>')
    await passwordInput.setValue('123')
    await loginButton.trigger('click')
    
    expect(wrapper.text()).toContain('密码长度不能少于6位')
  })

  it('should login successfully with valid credentials', async () => {
    const usernameInput = wrapper.find('input[placeholder="用户名"]')
    const passwordInput = wrapper.find('input[placeholder="密码"]')
    const rememberCheckbox = wrapper.find('.el-checkbox input')
    const loginButton = wrapper.find('button')
    
    await usernameInput.setValue('<EMAIL>')
    await passwordInput.setValue('password123')
    await rememberCheckbox.setChecked(true)
    
    // Mock successful login
    vi.spyOn(authStore, 'login').mockResolvedValue({ success: true })
    
    await loginButton.trigger('click')
    
    expect(authStore.login).toHaveBeenCalledWith({
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    })
    
    // Should redirect to home page
    await wrapper.vm.$nextTick()
    expect(mockPush).toHaveBeenCalledWith('/home')
  })

  it('should handle login failure', async () => {
    const usernameInput = wrapper.find('input[placeholder="用户名"]')
    const passwordInput = wrapper.find('input[placeholder="密码"]')
    const loginButton = wrapper.find('button')
    
    await usernameInput.setValue('<EMAIL>')
    await passwordInput.setValue('wrongpassword')
    
    // Mock failed login
    vi.spyOn(authStore, 'login').mockRejectedValue(new Error('Invalid credentials'))
    
    await loginButton.trigger('click')
    
    expect(authStore.login).toHaveBeenCalled()
    // Should not redirect on failure
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should show password reset dialog', async () => {
    const resetLink = wrapper.find('.el-link')
    
    await resetLink.trigger('click')
    
    expect(wrapper.find('.el-dialog').exists()).toBe(true)
    expect(wrapper.text()).toContain('重置密码')
  })

  it('should handle password reset', async () => {
    const resetLink = wrapper.find('.el-link')
    await resetLink.trigger('click')
    
    const emailInput = wrapper.find('.el-dialog input[placeholder="请输入邮箱"]')
    const sendButton = wrapper.find('.el-dialog .el-button--primary')
    
    await emailInput.setValue('<EMAIL>')
    await sendButton.trigger('click')
    
    expect(wrapper.text()).toContain('重置邮件已发送')
  })

  it('should validate email for password reset', async () => {
    const resetLink = wrapper.find('.el-link')
    await resetLink.trigger('click')
    
    const sendButton = wrapper.find('.el-dialog .el-button--primary')
    await sendButton.trigger('click')
    
    expect(wrapper.text()).toContain('请输入邮箱地址')
  })

  it('should support keyboard navigation', async () => {
    const passwordInput = wrapper.find('input[placeholder="密码"]')
    
    // Mock successful login
    vi.spyOn(authStore, 'login').mockResolvedValue({ success: true })
    
    // Fill form
    await wrapper.find('input[placeholder="用户名"]').setValue('<EMAIL>')
    await passwordInput.setValue('password123')
    
    // Press Enter on password field
    await passwordInput.trigger('keyup.enter')
    
    expect(authStore.login).toHaveBeenCalled()
  })
})
