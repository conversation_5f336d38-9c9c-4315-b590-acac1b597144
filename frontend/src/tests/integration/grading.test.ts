import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { ElButton, ElSelect, ElOption, ElInputNumber, ElCard } from 'element-plus'
import GradingPanel from '@/components/business/GradingPanel.vue'
import { useGradingStore } from '@/stores/grading'
import { useConfigStore } from '@/stores/config'

describe('Grading Integration', () => {
  let wrapper: any
  let gradingStore: any
  let configStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    gradingStore = useGradingStore()
    configStore = useConfigStore()
    
    // Mock config data
    configStore.urlOptions = [
      { id: 'preset1', name: '预设页面1', url: 'http://example.com/page1', actions: [] },
      { id: 'custom', name: '自定义URL', url: '', actions: [] }
    ]
    configStore.subjects = ['语文', '数学', '英语']
    
    wrapper = mount(GradingPanel, {
      global: {
        components: {
          ElButton,
          ElSelect,
          ElOption,
          ElInputNumber,
          ElCard
        },
        stubs: {
          'el-icon': true,
          'el-form': true,
          'el-form-item': true,
          'el-input': true,
          'el-textarea': true,
          'el-progress': true,
          'GradingCriteriaDialog': true
        }
      }
    })
    
    vi.clearAllMocks()
  })

  it('should render grading configuration form', () => {
    expect(wrapper.text()).toContain('阅卷配置')
    expect(wrapper.text()).toContain('阅卷页面')
    expect(wrapper.text()).toContain('科目')
    expect(wrapper.text()).toContain('评分标准')
    expect(wrapper.text()).toContain('试卷数量')
  })

  it('should render operation buttons', () => {
    expect(wrapper.text()).toContain('打开阅卷页面')
    expect(wrapper.text()).toContain('抓取图片')
    expect(wrapper.text()).toContain('开始自动阅卷')
  })

  it('should disable buttons when no URL selected', () => {
    const openButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('打开阅卷页面')
    )
    const fetchButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('抓取图片')
    )
    
    expect(openButton.attributes('disabled')).toBeDefined()
    expect(fetchButton.attributes('disabled')).toBeDefined()
  })

  it('should enable buttons when URL is selected', async () => {
    // Select a URL
    gradingStore.setSelectedUrl('preset1')
    await wrapper.vm.$nextTick()
    
    const openButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('打开阅卷页面')
    )
    const fetchButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('抓取图片')
    )
    
    expect(openButton.attributes('disabled')).toBeUndefined()
    expect(fetchButton.attributes('disabled')).toBeUndefined()
  })

  it('should show area selection for custom URL', async () => {
    gradingStore.setSelectedUrl('custom')
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('答题区域')
    expect(wrapper.text()).toContain('选择区域')
  })

  it('should disable auto grading when conditions not met', () => {
    const autoGradingButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('开始自动阅卷')
    )
    
    expect(autoGradingButton.attributes('disabled')).toBeDefined()
  })

  it('should enable auto grading when conditions are met', async () => {
    // Set up conditions
    gradingStore.setSelectedUrl('preset1')
    gradingStore.setGradingCriteria('Test criteria')
    await wrapper.vm.$nextTick()
    
    const autoGradingButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('开始自动阅卷')
    )
    
    expect(autoGradingButton.attributes('disabled')).toBeUndefined()
  })

  it('should handle URL opening', async () => {
    gradingStore.setSelectedUrl('preset1')
    await wrapper.vm.$nextTick()
    
    const openButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('打开阅卷页面')
    )
    
    await openButton.trigger('click')
    
    // Should show success message (mocked in setup)
    expect(wrapper.emitted()).toBeDefined()
  })

  it('should handle image fetching', async () => {
    gradingStore.setSelectedUrl('preset1')
    await wrapper.vm.$nextTick()
    
    // Mock successful image fetch
    vi.spyOn(gradingStore, 'fetchImage').mockResolvedValue({
      success: true,
      data: 'data:image/png;base64,test'
    })
    
    const fetchButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('抓取图片')
    )
    
    await fetchButton.trigger('click')
    
    expect(gradingStore.fetchImage).toHaveBeenCalled()
  })

  it('should handle auto grading start', async () => {
    // Set up conditions
    gradingStore.setSelectedUrl('preset1')
    gradingStore.setGradingCriteria('Test criteria')
    await wrapper.vm.$nextTick()
    
    // Mock successful auto grading start
    vi.spyOn(gradingStore, 'startAutoGrading').mockResolvedValue({ success: true })
    
    const autoGradingButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('开始自动阅卷')
    )
    
    await autoGradingButton.trigger('click')
    
    expect(gradingStore.startAutoGrading).toHaveBeenCalledWith(1) // default paper count
  })

  it('should handle auto grading stop', async () => {
    // Set grading in progress
    gradingStore.isAutoGrading = true
    await wrapper.vm.$nextTick()
    
    // Mock successful auto grading stop
    vi.spyOn(gradingStore, 'stopAutoGrading').mockResolvedValue({ success: true })
    
    const autoGradingButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('停止阅卷')
    )
    
    await autoGradingButton.trigger('click')
    
    expect(gradingStore.stopAutoGrading).toHaveBeenCalled()
  })

  it('should show progress when auto grading', async () => {
    gradingStore.isAutoGrading = true
    gradingStore.currentPaper = 3
    gradingStore.paperCount = 10
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('正在阅卷第 3 / 10 份试卷')
    expect(wrapper.find('.el-progress').exists()).toBe(true)
  })

  it('should show image preview when image is available', async () => {
    gradingStore.currentImage = 'data:image/png;base64,test'
    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('图片预览')
    expect(wrapper.find('img').exists()).toBe(true)
  })

  it('should handle image clearing', async () => {
    gradingStore.currentImage = 'data:image/png;base64,test'
    await wrapper.vm.$nextTick()
    
    vi.spyOn(gradingStore, 'clearCurrentImage')
    
    const clearButton = wrapper.find('.card-header .el-button')
    await clearButton.trigger('click')
    
    expect(gradingStore.clearCurrentImage).toHaveBeenCalled()
  })

  it('should open criteria dialog', async () => {
    const editButton = wrapper.findAll('button').find((btn: any) => 
      btn.text().includes('编辑评分标准')
    )
    
    await editButton.trigger('click')
    
    expect(wrapper.vm.showCriteriaDialog).toBe(true)
  })

  it('should handle criteria save', async () => {
    const newCriteria = 'Updated criteria'
    
    vi.spyOn(gradingStore, 'setGradingCriteria')
    
    await wrapper.vm.handleCriteriaSave(newCriteria)
    
    expect(gradingStore.setGradingCriteria).toHaveBeenCalledWith(newCriteria)
    expect(wrapper.vm.gradingConfig.criteria).toBe(newCriteria)
  })
})
