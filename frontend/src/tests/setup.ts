import { vi } from 'vitest'

// Mock Element Plus message
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue('confirm')
    }
  }
})

// Mock Vue Router
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  useRoute: () => ({
    params: {},
    query: {},
    path: '/',
    name: 'home'
  })
}))

// Mock Wails runtime
global.window = Object.assign(global.window, {
  go: {
    main: {
      App: {
        GetVersion: vi.fn().mockResolvedValue('v0.1.0'),
        OpenURL: vi.fn().mockResolvedValue(undefined),
        SelectDirectory: vi.fn().mockResolvedValue('/mock/directory'),
        ScreenshotArea: vi.fn().mockResolvedValue('data:image/png;base64,mock'),
        ClickElement: vi.fn().mockResolvedValue(undefined),
        FillElement: vi.fn().mockResolvedValue(undefined)
      }
    }
  }
})
