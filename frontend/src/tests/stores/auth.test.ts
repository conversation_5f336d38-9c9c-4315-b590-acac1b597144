import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useAuthStore } from '@/stores/auth'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with default state', () => {
    const authStore = useAuthStore()
    
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.userInfo).toBeNull()
    expect(authStore.token).toBe('')
    expect(authStore.config).toBeNull()
    expect(authStore.isLoggedIn).toBe(false)
  })

  it('should login successfully', async () => {
    const authStore = useAuthStore()
    const credentials = {
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    }

    const result = await authStore.login(credentials)

    expect(result.success).toBe(true)
    expect(authStore.isAuthenticated).toBe(true)
    expect(authStore.userInfo).toEqual({
      email: '<EMAIL>',
      name: '<EMAIL>'
    })
    expect(authStore.token).toBe('mock-token')
    expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'mock-token')
    expect(localStorageMock.setItem).toHaveBeenCalledWith('user_info', JSON.stringify({
      email: '<EMAIL>',
      name: '<EMAIL>'
    }))
  })

  it('should not save to localStorage when rememberMe is false', async () => {
    const authStore = useAuthStore()
    const credentials = {
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: false
    }

    await authStore.login(credentials)

    expect(localStorageMock.setItem).not.toHaveBeenCalled()
  })

  it('should logout successfully', () => {
    const authStore = useAuthStore()
    
    // Set initial state
    authStore.isAuthenticated = true
    authStore.userInfo = { email: '<EMAIL>', name: 'test' }
    authStore.token = 'test-token'

    authStore.logout()

    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.userInfo).toBeNull()
    expect(authStore.token).toBe('')
    expect(authStore.config).toBeNull()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('user_info')
  })

  it('should initialize auth from localStorage', () => {
    const mockUserInfo = { email: '<EMAIL>', name: 'test' }
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'auth_token') return 'saved-token'
      if (key === 'user_info') return JSON.stringify(mockUserInfo)
      return null
    })

    const authStore = useAuthStore()
    authStore.initializeAuth()

    expect(authStore.isAuthenticated).toBe(true)
    expect(authStore.token).toBe('saved-token')
    expect(authStore.userInfo).toEqual(mockUserInfo)
  })

  it('should handle corrupted localStorage data', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'auth_token') return 'saved-token'
      if (key === 'user_info') return 'invalid-json'
      return null
    })

    const authStore = useAuthStore()
    authStore.initializeAuth()

    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.token).toBe('')
    expect(authStore.userInfo).toBeNull()
  })

  it('should update config', () => {
    const authStore = useAuthStore()
    const newConfig = { apiUrl: 'http://localhost:3000' }

    authStore.updateConfig(newConfig)

    expect(authStore.config).toEqual(newConfig)
  })

  it('should merge config updates', () => {
    const authStore = useAuthStore()
    authStore.config = { apiUrl: 'http://localhost:3000', timeout: 5000 }
    
    const configUpdate = { timeout: 10000, retries: 3 }
    authStore.updateConfig(configUpdate)

    expect(authStore.config).toEqual({
      apiUrl: 'http://localhost:3000',
      timeout: 10000,
      retries: 3
    })
  })
})
