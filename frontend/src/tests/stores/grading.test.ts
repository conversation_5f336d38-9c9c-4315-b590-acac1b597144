import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useGradingStore } from '@/stores/grading'

describe('Grading Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with default state', () => {
    const gradingStore = useGradingStore()
    
    expect(gradingStore.isAutoGrading).toBe(false)
    expect(gradingStore.currentImage).toBe('')
    expect(gradingStore.gradingCriteria).toBe('')
    expect(gradingStore.selectedUrl).toBe('')
    expect(gradingStore.selectedAreaCoords).toBeNull()
    expect(gradingStore.gradingRecords).toEqual([])
    expect(gradingStore.paperCount).toBe(1)
    expect(gradingStore.currentPaper).toBe(0)
  })

  it('should compute hasImage correctly', () => {
    const gradingStore = useGradingStore()
    
    expect(gradingStore.hasImage).toBe(false)
    
    gradingStore.currentImage = 'data:image/png;base64,test'
    expect(gradingStore.hasImage).toBe(true)
  })

  it('should compute hasAreaSelected correctly', () => {
    const gradingStore = useGradingStore()
    
    expect(gradingStore.hasAreaSelected).toBe(false)
    
    gradingStore.selectedAreaCoords = { x: 0, y: 0, width: 100, height: 100 }
    expect(gradingStore.hasAreaSelected).toBe(true)
  })

  it('should compute canStartGrading correctly', () => {
    const gradingStore = useGradingStore()
    
    // Initially should not be able to start grading
    expect(gradingStore.canStartGrading).toBe(false)
    
    // Set criteria but no URL
    gradingStore.setGradingCriteria('Test criteria')
    expect(gradingStore.canStartGrading).toBe(false)
    
    // Set non-custom URL
    gradingStore.setSelectedUrl('preset-url')
    expect(gradingStore.canStartGrading).toBe(true)
    
    // Set custom URL without area selection
    gradingStore.setSelectedUrl('custom')
    expect(gradingStore.canStartGrading).toBe(false)
    
    // Set custom URL with area selection
    gradingStore.setSelectedAreaCoords({ x: 0, y: 0, width: 100, height: 100 })
    expect(gradingStore.canStartGrading).toBe(true)
  })

  it('should start auto grading successfully', async () => {
    const gradingStore = useGradingStore()
    
    // Setup prerequisites
    gradingStore.setGradingCriteria('Test criteria')
    gradingStore.setSelectedUrl('preset-url')
    
    const result = await gradingStore.startAutoGrading(5)
    
    expect(result.success).toBe(true)
    expect(gradingStore.isAutoGrading).toBe(true)
    expect(gradingStore.paperCount).toBe(5)
    expect(gradingStore.currentPaper).toBe(0)
  })

  it('should not start auto grading when already running', async () => {
    const gradingStore = useGradingStore()
    gradingStore.isAutoGrading = true
    
    await expect(gradingStore.startAutoGrading(5)).rejects.toThrow('自动阅卷已在进行中')
  })

  it('should not start auto grading when conditions not met', async () => {
    const gradingStore = useGradingStore()
    
    await expect(gradingStore.startAutoGrading(5)).rejects.toThrow('请先配置阅卷条件')
  })

  it('should stop auto grading successfully', async () => {
    const gradingStore = useGradingStore()
    gradingStore.isAutoGrading = true
    gradingStore.currentPaper = 3
    
    const result = await gradingStore.stopAutoGrading()
    
    expect(result.success).toBe(true)
    expect(gradingStore.isAutoGrading).toBe(false)
    expect(gradingStore.currentPaper).toBe(0)
  })

  it('should fetch image successfully', async () => {
    const gradingStore = useGradingStore()
    gradingStore.setSelectedUrl('preset-url')
    
    const result = await gradingStore.fetchImage()
    
    expect(result.success).toBe(true)
    expect(result.data).toBe('data:image/png;base64,mock-image-data')
    expect(gradingStore.currentImage).toBe('data:image/png;base64,mock-image-data')
  })

  it('should fetch area image when custom URL and area selected', async () => {
    const gradingStore = useGradingStore()
    gradingStore.setSelectedUrl('custom')
    gradingStore.setSelectedAreaCoords({ x: 10, y: 20, width: 100, height: 200 })
    
    const result = await gradingStore.fetchImage()
    
    expect(result.success).toBe(true)
    expect(gradingStore.currentImage).toBe('data:image/png;base64,mock-image-data')
  })

  it('should submit grade successfully', async () => {
    const gradingStore = useGradingStore()
    gradingStore.setSelectedUrl('preset-url')
    
    const result = await gradingStore.submitGrade(85)
    
    expect(result.success).toBe(true)
  })

  it('should load grading records successfully', async () => {
    const gradingStore = useGradingStore()
    
    const result = await gradingStore.loadGradingRecords()
    
    expect(result.success).toBe(true)
    expect(gradingStore.gradingRecords).toEqual([])
  })

  it('should set grading criteria', () => {
    const gradingStore = useGradingStore()
    const criteria = 'Test grading criteria'
    
    gradingStore.setGradingCriteria(criteria)
    
    expect(gradingStore.gradingCriteria).toBe(criteria)
  })

  it('should set selected URL and clear area coords for non-custom URL', () => {
    const gradingStore = useGradingStore()
    gradingStore.setSelectedAreaCoords({ x: 0, y: 0, width: 100, height: 100 })
    
    gradingStore.setSelectedUrl('preset-url')
    
    expect(gradingStore.selectedUrl).toBe('preset-url')
    expect(gradingStore.selectedAreaCoords).toBeNull()
  })

  it('should set selected URL and keep area coords for custom URL', () => {
    const gradingStore = useGradingStore()
    const coords = { x: 0, y: 0, width: 100, height: 100 }
    gradingStore.setSelectedAreaCoords(coords)
    
    gradingStore.setSelectedUrl('custom')
    
    expect(gradingStore.selectedUrl).toBe('custom')
    expect(gradingStore.selectedAreaCoords).toEqual(coords)
  })

  it('should set selected area coordinates', () => {
    const gradingStore = useGradingStore()
    const coords = { x: 10, y: 20, width: 300, height: 400 }
    
    gradingStore.setSelectedAreaCoords(coords)
    
    expect(gradingStore.selectedAreaCoords).toEqual(coords)
  })

  it('should clear current image', () => {
    const gradingStore = useGradingStore()
    gradingStore.currentImage = 'data:image/png;base64,test'
    
    gradingStore.clearCurrentImage()
    
    expect(gradingStore.currentImage).toBe('')
  })

  it('should update progress', () => {
    const gradingStore = useGradingStore()
    
    gradingStore.updateProgress(3)
    
    expect(gradingStore.currentPaper).toBe(3)
  })
})
