import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from '@/App.vue'
import LoginView from '@/views/login/LoginView.vue'
import HomeView from '@/views/home/<USER>'
import { useAuthStore } from '@/stores/auth'
import { useGradingStore } from '@/stores/grading'
import { useConfigStore } from '@/stores/config'

// Mock Wails runtime
global.window = Object.assign(global.window, {
  go: {
    main: {
      App: {
        GetVersion: vi.fn().mockResolvedValue('v0.1.0'),
        OpenURL: vi.fn().mockResolvedValue(undefined),
        SelectDirectory: vi.fn().mockResolvedValue('/mock/directory'),
        ScreenshotArea: vi.fn().mockResolvedValue('data:image/png;base64,mock'),
        ClickElement: vi.fn().mockResolvedValue(undefined),
        FillElement: vi.fn().mockResolvedValue(undefined)
      }
    }
  }
})

describe('Complete User Workflow E2E', () => {
  let router: any
  let wrapper: any
  let authStore: any
  let gradingStore: any
  let configStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())
    
    authStore = useAuthStore()
    gradingStore = useGradingStore()
    configStore = useConfigStore()
    
    // Create router
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/login', component: LoginView },
        { path: '/home', component: HomeView },
        { path: '/', redirect: '/login' }
      ]
    })
    
    // Mock config data
    configStore.urlOptions = [
      { id: 'preset1', name: '预设页面1', url: 'http://example.com/page1', actions: [] },
      { id: 'custom', name: '自定义URL', url: '', actions: [] }
    ]
    configStore.subjects = ['语文', '数学', '英语']
    
    wrapper = mount(App, {
      global: {
        plugins: [router],
        stubs: {
          'el-icon': true,
          'el-button': true,
          'el-input': true,
          'el-form': true,
          'el-form-item': true,
          'el-checkbox': true,
          'el-select': true,
          'el-option': true,
          'el-card': true,
          'el-container': true,
          'el-aside': true,
          'el-header': true,
          'el-main': true,
          'el-menu': true,
          'el-menu-item': true,
          'el-dropdown': true,
          'el-dropdown-menu': true,
          'el-dropdown-item': true,
          'el-table': true,
          'el-table-column': true,
          'el-pagination': true,
          'el-dialog': true,
          'el-progress': true,
          'el-input-number': true,
          'el-textarea': true,
          'el-switch': true,
          'el-radio-group': true,
          'el-radio': true,
          'el-checkbox-group': true,
          'el-date-picker': true,
          'el-descriptions': true,
          'el-descriptions-item': true,
          'el-tag': true,
          'el-tooltip': true,
          'el-link': true
        }
      }
    })
    
    vi.clearAllMocks()
  })

  it('should complete full user workflow: login -> configure -> grade -> export', async () => {
    // Step 1: Start at login page
    await router.push('/login')
    await wrapper.vm.$nextTick()
    
    expect(router.currentRoute.value.path).toBe('/login')
    
    // Step 2: Login with valid credentials
    vi.spyOn(authStore, 'login').mockResolvedValue({ success: true })
    
    // Simulate login form submission
    await authStore.login({
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    })
    
    expect(authStore.login).toHaveBeenCalledWith({
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    })
    
    // Step 3: Navigate to home page after login
    await router.push('/home')
    await wrapper.vm.$nextTick()
    
    expect(router.currentRoute.value.path).toBe('/home')
    
    // Step 4: Configure grading settings
    gradingStore.setSelectedUrl('preset1')
    gradingStore.setGradingCriteria('Test grading criteria')
    configStore.setSelectedSubject('语文')
    
    expect(gradingStore.selectedUrl).toBe('preset1')
    expect(gradingStore.gradingCriteria).toBe('Test grading criteria')
    expect(configStore.selectedSubject).toBe('语文')
    
    // Step 5: Fetch image
    vi.spyOn(gradingStore, 'fetchImage').mockResolvedValue({
      success: true,
      data: 'data:image/png;base64,mockimage'
    })
    
    await gradingStore.fetchImage()
    
    expect(gradingStore.fetchImage).toHaveBeenCalled()
    expect(gradingStore.currentImage).toBe('data:image/png;base64,mockimage')
    
    // Step 6: Start auto grading
    vi.spyOn(gradingStore, 'startAutoGrading').mockResolvedValue({ success: true })
    
    await gradingStore.startAutoGrading(5)
    
    expect(gradingStore.startAutoGrading).toHaveBeenCalledWith(5)
    expect(gradingStore.isAutoGrading).toBe(true)
    expect(gradingStore.paperCount).toBe(5)
    
    // Step 7: Simulate grading progress
    gradingStore.updateProgress(3)
    expect(gradingStore.currentPaper).toBe(3)
    
    // Step 8: Complete grading
    vi.spyOn(gradingStore, 'stopAutoGrading').mockResolvedValue({ success: true })
    
    await gradingStore.stopAutoGrading()
    
    expect(gradingStore.stopAutoGrading).toHaveBeenCalled()
    expect(gradingStore.isAutoGrading).toBe(false)
    
    // Step 9: Load grading records
    const mockRecords = [
      {
        id: 1,
        userEmail: '<EMAIL>',
        answerText: 'Student answer 1',
        score: 85,
        scoreDetails: 'Good answer',
        answerImage: 'data:image/png;base64,answer1',
        createdAt: '2024-01-15T10:00:00Z'
      },
      {
        id: 2,
        userEmail: '<EMAIL>',
        answerText: 'Student answer 2',
        score: 92,
        scoreDetails: 'Excellent answer',
        answerImage: 'data:image/png;base64,answer2',
        createdAt: '2024-01-15T10:05:00Z'
      }
    ]
    
    vi.spyOn(gradingStore, 'loadGradingRecords').mockResolvedValue({ success: true })
    gradingStore.gradingRecords = mockRecords
    
    await gradingStore.loadGradingRecords()
    
    expect(gradingStore.loadGradingRecords).toHaveBeenCalled()
    expect(gradingStore.gradingRecords).toEqual(mockRecords)
    
    // Step 10: Save configuration
    vi.spyOn(configStore, 'saveConfig').mockResolvedValue({ success: true })
    
    await configStore.saveConfig()
    
    expect(configStore.saveConfig).toHaveBeenCalled()
    
    // Step 11: Logout
    authStore.logout()
    
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.userInfo).toBeNull()
    expect(authStore.token).toBe('')
  })

  it('should handle error scenarios gracefully', async () => {
    // Test login failure
    vi.spyOn(authStore, 'login').mockRejectedValue(new Error('Invalid credentials'))
    
    try {
      await authStore.login({
        username: '<EMAIL>',
        password: 'wrongpassword',
        rememberMe: false
      })
    } catch (error) {
      expect(error.message).toBe('Invalid credentials')
    }
    
    expect(authStore.isAuthenticated).toBe(false)
    
    // Test image fetch failure
    vi.spyOn(gradingStore, 'fetchImage').mockRejectedValue(new Error('Network error'))
    
    try {
      await gradingStore.fetchImage()
    } catch (error) {
      expect(error.message).toBe('Network error')
    }
    
    expect(gradingStore.currentImage).toBe('')
    
    // Test auto grading failure
    vi.spyOn(gradingStore, 'startAutoGrading').mockRejectedValue(new Error('Configuration error'))
    
    try {
      await gradingStore.startAutoGrading(5)
    } catch (error) {
      expect(error.message).toBe('Configuration error')
    }
    
    expect(gradingStore.isAutoGrading).toBe(false)
  })

  it('should maintain state consistency across navigation', async () => {
    // Login
    vi.spyOn(authStore, 'login').mockResolvedValue({ success: true })
    await authStore.login({
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    })
    
    // Set grading configuration
    gradingStore.setSelectedUrl('preset1')
    gradingStore.setGradingCriteria('Persistent criteria')
    
    // Navigate between pages
    await router.push('/home')
    await wrapper.vm.$nextTick()
    
    // State should persist
    expect(authStore.isAuthenticated).toBe(true)
    expect(gradingStore.selectedUrl).toBe('preset1')
    expect(gradingStore.gradingCriteria).toBe('Persistent criteria')
    
    // Navigate to different section
    await router.push('/login')
    await router.push('/home')
    await wrapper.vm.$nextTick()
    
    // State should still persist
    expect(authStore.isAuthenticated).toBe(true)
    expect(gradingStore.selectedUrl).toBe('preset1')
    expect(gradingStore.gradingCriteria).toBe('Persistent criteria')
  })

  it('should handle concurrent operations correctly', async () => {
    // Setup authenticated state
    vi.spyOn(authStore, 'login').mockResolvedValue({ success: true })
    await authStore.login({
      username: '<EMAIL>',
      password: 'password123',
      rememberMe: false
    })
    
    gradingStore.setSelectedUrl('preset1')
    gradingStore.setGradingCriteria('Test criteria')
    
    // Start multiple operations concurrently
    const fetchImagePromise = gradingStore.fetchImage()
    const loadRecordsPromise = gradingStore.loadGradingRecords()
    const saveConfigPromise = configStore.saveConfig()
    
    // Mock all operations to succeed
    vi.spyOn(gradingStore, 'fetchImage').mockResolvedValue({ success: true, data: 'image' })
    vi.spyOn(gradingStore, 'loadGradingRecords').mockResolvedValue({ success: true })
    vi.spyOn(configStore, 'saveConfig').mockResolvedValue({ success: true })
    
    // Wait for all operations to complete
    const results = await Promise.all([
      fetchImagePromise,
      loadRecordsPromise,
      saveConfigPromise
    ])
    
    // All operations should succeed
    expect(results).toHaveLength(3)
    expect(gradingStore.fetchImage).toHaveBeenCalled()
    expect(gradingStore.loadGradingRecords).toHaveBeenCalled()
    expect(configStore.saveConfig).toHaveBeenCalled()
  })
})
