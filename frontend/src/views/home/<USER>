<template>
  <el-container class="home-container">
    <!-- 侧边栏 -->
    <el-aside width="250px" class="sidebar">
      <div class="logo">
        <h2>山竹阅卷</h2>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="grading">
          <el-icon><Document /></el-icon>
          <span>智能阅卷</span>
        </el-menu-item>
        
        <el-menu-item index="records" v-if="isAuthenticated">
          <el-icon><List /></el-icon>
          <span>阅卷记录</span>
        </el-menu-item>
        
        <el-menu-item index="export" v-if="isAuthenticated">
          <el-icon><Download /></el-icon>
          <span>导出数据</span>
        </el-menu-item>
        
        <el-menu-item index="settings">
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <h3>{{ pageTitle }}</h3>
        </div>
        
        <div class="header-right">
          <el-dropdown v-if="isAuthenticated" @command="handleUserCommand">
            <span class="user-info">
              <el-icon><User /></el-icon>
              {{ userInfo?.email }}
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <el-button v-else type="primary" @click="$router.push('/login')">
            登录
          </el-button>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <component :is="currentComponent" />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document,
  List,
  Download,
  Setting,
  User,
  ArrowDown
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// 异步加载组件
const GradingPanel = defineAsyncComponent(() => import('@/components/business/GradingPanel.vue'))
const RecordsPanel = defineAsyncComponent(() => import('@/components/business/RecordsPanel.vue'))
const ExportPanel = defineAsyncComponent(() => import('@/components/business/ExportPanel.vue'))
const SettingsPanel = defineAsyncComponent(() => import('@/components/business/SettingsPanel.vue'))

const router = useRouter()
const authStore = useAuthStore()

const activeMenu = ref('grading')

const isAuthenticated = computed(() => authStore.isAuthenticated)
const userInfo = computed(() => authStore.userInfo)

const pageTitle = computed(() => {
  const titles: Record<string, string> = {
    grading: '智能阅卷',
    records: '阅卷记录',
    export: '导出数据',
    settings: '系统设置'
  }
  return titles[activeMenu.value] || '山竹阅卷'
})

const currentComponent = computed(() => {
  const components: Record<string, any> = {
    grading: GradingPanel,
    records: RecordsPanel,
    export: ExportPanel,
    settings: SettingsPanel
  }
  return components[activeMenu.value] || GradingPanel
})

const handleMenuSelect = (index: string) => {
  activeMenu.value = index
}

const handleUserCommand = (command: string) => {
  if (command === 'logout') {
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  }
}
</script>

<style scoped>
.home-container {
  height: 100vh;
}

.sidebar {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  border-right: 1px solid #dee2e6;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.logo h2 {
  margin: 0;
  color: #495057;
  font-size: 20px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.sidebar-menu .el-menu-item {
  margin: 5px 10px;
  border-radius: 8px;
  color: #495057;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #e9ecef;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #007bff;
  color: white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: #f8f9fa;
}

.main-content {
  padding: 20px;
  background-color: #f8f9fa;
  overflow-y: auto;
}
</style>
