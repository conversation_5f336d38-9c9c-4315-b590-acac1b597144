<template>
  <el-dialog
    :model-value="modelValue"
    title="编辑评分标准"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <el-form :model="criteriaForm" label-width="120px">
      <el-form-item label="默认评分标准">
        <el-input
          v-model="criteriaForm.defaultCriteria"
          type="textarea"
          :rows="3"
          placeholder="请输入默认评分标准..."
        />
      </el-form-item>

      <el-form-item label="得分点">
        <el-input
          v-model="criteriaForm.scoringPoints"
          type="textarea"
          :rows="3"
          placeholder="请输入得分点..."
        />
      </el-form-item>

      <el-form-item label="扣分点">
        <el-input
          v-model="criteriaForm.deductionPoints"
          type="textarea"
          :rows="3"
          placeholder="请输入扣分点..."
        />
      </el-form-item>

      <el-form-item label="总分">
        <el-input-number
          v-model="criteriaForm.totalScore"
          :min="1"
          :max="100"
          style="width: 200px"
        />
      </el-form-item>
    </el-form>

    <div class="preview-section">
      <h4>预览</h4>
      <div class="criteria-preview">
        {{ formattedCriteria }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface CriteriaForm {
  defaultCriteria: string
  scoringPoints: string
  deductionPoints: string
  totalScore: number
}

interface Props {
  modelValue: boolean
  criteria?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', criteria: string): void
}

const props = withDefaults(defineProps<Props>(), {
  criteria: ''
})

const emit = defineEmits<Emits>()

const criteriaForm = ref<CriteriaForm>({
  defaultCriteria: '',
  scoringPoints: '',
  deductionPoints: '',
  totalScore: 10
})

const formattedCriteria = computed(() => {
  const parts = []
  
  if (criteriaForm.value.defaultCriteria) {
    parts.push(`默认评分标准：\n${criteriaForm.value.defaultCriteria}`)
  }
  
  if (criteriaForm.value.scoringPoints) {
    parts.push(`得分点：\n${criteriaForm.value.scoringPoints}`)
  }
  
  if (criteriaForm.value.deductionPoints) {
    parts.push(`扣分点：\n${criteriaForm.value.deductionPoints}`)
  }
  
  parts.push(`总分：${criteriaForm.value.totalScore}分`)
  
  return parts.join('\n\n')
})

const parseCriteria = (criteria: string) => {
  if (!criteria) return

  try {
    // 尝试解析结构化的评分标准
    const lines = criteria.split('\n')
    let currentSection = ''
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      
      if (trimmedLine.startsWith('默认评分标准：')) {
        currentSection = 'default'
        criteriaForm.value.defaultCriteria = trimmedLine.replace('默认评分标准：', '').trim()
      } else if (trimmedLine.startsWith('得分点：')) {
        currentSection = 'scoring'
        criteriaForm.value.scoringPoints = trimmedLine.replace('得分点：', '').trim()
      } else if (trimmedLine.startsWith('扣分点：')) {
        currentSection = 'deduction'
        criteriaForm.value.deductionPoints = trimmedLine.replace('扣分点：', '').trim()
      } else if (trimmedLine.startsWith('总分：')) {
        const scoreMatch = trimmedLine.match(/(\d+)/)
        if (scoreMatch) {
          criteriaForm.value.totalScore = parseInt(scoreMatch[1])
        }
      } else if (trimmedLine && currentSection) {
        // 继续当前部分的内容
        switch (currentSection) {
          case 'default':
            criteriaForm.value.defaultCriteria += '\n' + trimmedLine
            break
          case 'scoring':
            criteriaForm.value.scoringPoints += '\n' + trimmedLine
            break
          case 'deduction':
            criteriaForm.value.deductionPoints += '\n' + trimmedLine
            break
        }
      }
    }
  } catch (error) {
    // 如果解析失败，将整个内容放到默认评分标准中
    criteriaForm.value.defaultCriteria = criteria
  }
}

const handleSave = () => {
  emit('save', formattedCriteria.value)
  emit('update:modelValue', false)
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

// 监听对话框打开，初始化表单数据
watch(() => props.modelValue, (newValue) => {
  if (newValue && props.criteria) {
    parseCriteria(props.criteria)
  }
})

// 监听criteria属性变化
watch(() => props.criteria, (newCriteria) => {
  if (newCriteria) {
    parseCriteria(newCriteria)
  }
})
</script>

<style scoped>
.preview-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.criteria-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.6;
  color: #495057;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
