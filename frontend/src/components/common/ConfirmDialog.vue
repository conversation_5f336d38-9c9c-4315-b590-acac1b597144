<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    :width="width"
    :center="center"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div class="confirm-content">
      <div v-if="icon" class="confirm-icon">
        <el-icon :size="48" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      
      <div class="confirm-message">
        <p v-if="message">{{ message }}</p>
        <slot v-else />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ cancelText }}
        </el-button>
        <el-button
          :type="confirmType"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Warning, QuestionFilled, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  title?: string
  message?: string
  type?: 'warning' | 'info' | 'success' | 'error'
  confirmText?: string
  cancelText?: string
  width?: string
  center?: boolean
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认',
  type: 'warning',
  confirmText: '确定',
  cancelText: '取消',
  width: '400px',
  center: true,
  loading: false
})

const emit = defineEmits<Emits>()

const icon = computed(() => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: SuccessFilled,
    error: Warning
  }
  return iconMap[props.type]
})

const iconColor = computed(() => {
  const colorMap = {
    warning: '#E6A23C',
    info: '#409EFF',
    success: '#67C23A',
    error: '#F56C6C'
  }
  return colorMap[props.type]
})

const confirmType = computed(() => {
  const typeMap = {
    warning: 'warning',
    info: 'primary',
    success: 'success',
    error: 'danger'
  }
  return typeMap[props.type] as any
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 0;
}

.confirm-icon {
  flex-shrink: 0;
}

.confirm-message {
  flex: 1;
}

.confirm-message p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
