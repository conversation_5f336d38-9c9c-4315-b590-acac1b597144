<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    :width="width"
    :center="center"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div class="loading-content">
      <div class="loading-spinner">
        <el-icon :size="48" class="is-loading">
          <Loading />
        </el-icon>
      </div>
      
      <div class="loading-message">
        <p>{{ message }}</p>
        <div v-if="showProgress" class="progress-container">
          <el-progress
            :percentage="progress"
            :status="progressStatus"
            :stroke-width="8"
          />
          <p v-if="progressText" class="progress-text">{{ progressText }}</p>
        </div>
      </div>
    </div>

    <template #footer v-if="showCancel">
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ cancelText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Loading } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  title?: string
  message?: string
  width?: string
  center?: boolean
  showProgress?: boolean
  progress?: number
  progressStatus?: 'success' | 'exception' | 'warning' | ''
  progressText?: string
  showCancel?: boolean
  cancelText?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '加载中',
  message: '请稍候...',
  width: '400px',
  center: true,
  showProgress: false,
  progress: 0,
  progressStatus: '',
  showCancel: false,
  cancelText: '取消'
})

const emit = defineEmits<Emits>()

const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 20px;
}

.loading-message p {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.progress-container {
  width: 100%;
  margin-top: 16px;
}

.progress-text {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #666;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
