<template>
  <div class="grading-panel">
    <!-- 配置区域 -->
    <el-card class="config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>阅卷配置</span>
        </div>
      </template>

      <el-form :model="gradingConfig" label-width="100px">
        <!-- URL选择 -->
        <el-form-item label="阅卷页面">
          <el-select
            v-model="gradingConfig.selectedUrl"
            placeholder="请选择阅卷页面"
            style="width: 100%"
            @change="handleUrlChange"
          >
            <el-option
              v-for="option in urlOptions"
              :key="option.id"
              :label="option.name"
              :value="option.id"
            />
          </el-select>
        </el-form-item>

        <!-- 科目选择 -->
        <el-form-item label="科目">
          <el-select
            v-model="gradingConfig.selectedSubject"
            placeholder="请选择科目"
            style="width: 100%"
          >
            <el-option
              v-for="subject in subjects"
              :key="subject"
              :label="subject"
              :value="subject"
            />
          </el-select>
        </el-form-item>

        <!-- 评分标准 -->
        <el-form-item label="评分标准">
          <div class="criteria-container">
            <el-input
              v-model="gradingConfig.criteria"
              type="textarea"
              :rows="4"
              placeholder="请输入评分标准..."
              readonly
            />
            <el-button
              type="primary"
              size="small"
              @click="showCriteriaDialog = true"
              style="margin-top: 8px"
            >
              编辑评分标准
            </el-button>
            <span class="criteria-hint">点击编辑可自定义评分标准</span>
          </div>
        </el-form-item>

        <!-- 区域选择 -->
        <el-form-item v-if="gradingConfig.selectedUrl === 'custom'" label="答题区域">
          <div class="area-selection">
            <el-button
              type="primary"
              :disabled="!gradingConfig.selectedUrl"
              @click="handleAreaSelect"
            >
              选择区域
            </el-button>
            <span v-if="hasAreaSelected" class="area-info">
              已选择区域: {{ selectedAreaCoords?.width }}×{{ selectedAreaCoords?.height }}
            </span>
          </div>
        </el-form-item>

        <!-- 试卷数量 -->
        <el-form-item label="试卷数量">
          <el-input-number
            v-model="gradingConfig.paperCount"
            :min="1"
            :max="1000"
            style="width: 200px"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>阅卷操作</span>
        </div>
      </template>

      <div class="operation-buttons">
        <el-button
          type="primary"
          size="large"
          @click="handleOpenUrl"
          :disabled="!gradingConfig.selectedUrl"
        >
          打开阅卷页面
        </el-button>

        <el-button
          type="success"
          size="large"
          @click="handleFetchImage"
          :disabled="!gradingConfig.selectedUrl"
          :loading="fetchingImage"
        >
          抓取图片
        </el-button>

        <el-button
          type="warning"
          size="large"
          class="auto-grading-btn"
          @click="handleAutoGrading"
          :disabled="!canStartGrading"
          :loading="isAutoGrading"
        >
          {{ isAutoGrading ? '停止阅卷' : '开始自动阅卷' }}
        </el-button>
      </div>

      <!-- 进度显示 -->
      <div v-if="isAutoGrading" class="progress-section">
        <el-progress
          :percentage="gradingProgress"
          :status="gradingProgress === 100 ? 'success' : undefined"
        />
        <p class="progress-text">
          正在阅卷第 {{ currentPaper }} / {{ paperCount }} 份试卷
        </p>
      </div>
    </el-card>

    <!-- 图片预览区域 -->
    <el-card v-if="currentImage" class="image-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>图片预览</span>
          <el-button type="text" @click="clearImage">清除</el-button>
        </div>
      </template>

      <div class="image-preview">
        <img :src="currentImage" alt="截图预览" />
      </div>
    </el-card>

    <!-- 评分标准对话框 -->
    <GradingCriteriaDialog
      v-model="showCriteriaDialog"
      :criteria="gradingConfig.criteria"
      @save="handleCriteriaSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useGradingStore } from '@/stores/grading'
import { useConfigStore } from '@/stores/config'
import GradingCriteriaDialog from '@/components/common/GradingCriteriaDialog.vue'

const gradingStore = useGradingStore()
const configStore = useConfigStore()

const showCriteriaDialog = ref(false)
const fetchingImage = ref(false)

const gradingConfig = ref({
  selectedUrl: '',
  selectedSubject: '',
  criteria: '',
  paperCount: 1
})

// 计算属性
const urlOptions = computed(() => configStore.urlOptions)
const subjects = computed(() => configStore.subjects)
const isAutoGrading = computed(() => gradingStore.isAutoGrading)
const currentImage = computed(() => gradingStore.currentImage)
const hasAreaSelected = computed(() => gradingStore.hasAreaSelected)
const selectedAreaCoords = computed(() => gradingStore.selectedAreaCoords)
const canStartGrading = computed(() => gradingStore.canStartGrading)
const currentPaper = computed(() => gradingStore.currentPaper)
const paperCount = computed(() => gradingStore.paperCount)

const gradingProgress = computed(() => {
  if (paperCount.value === 0) return 0
  return Math.round((currentPaper.value / paperCount.value) * 100)
})

// 方法
const handleUrlChange = (url: string) => {
  gradingStore.setSelectedUrl(url)
}

const handleAreaSelect = () => {
  // TODO: 实现区域选择逻辑
  ElMessage.info('区域选择功能开发中...')
}

const handleOpenUrl = async () => {
  try {
    // TODO: 调用后端打开URL
    ElMessage.success('页面已打开')
  } catch (error) {
    ElMessage.error(`打开页面失败: ${error}`)
  }
}

const handleFetchImage = async () => {
  try {
    fetchingImage.value = true
    await gradingStore.fetchImage()
    ElMessage.success('图片抓取成功')
  } catch (error) {
    ElMessage.error(`抓取失败: ${error}`)
  } finally {
    fetchingImage.value = false
  }
}

const handleAutoGrading = async () => {
  try {
    if (isAutoGrading.value) {
      await gradingStore.stopAutoGrading()
      ElMessage.success('自动阅卷已停止')
    } else {
      await gradingStore.startAutoGrading(gradingConfig.value.paperCount)
      ElMessage.success('自动阅卷已开始')
    }
  } catch (error) {
    ElMessage.error(`操作失败: ${error}`)
  }
}

const handleCriteriaSave = (criteria: string) => {
  gradingConfig.value.criteria = criteria
  gradingStore.setGradingCriteria(criteria)
  ElMessage.success('评分标准已保存')
}

const clearImage = () => {
  gradingStore.clearCurrentImage()
}

// 生命周期
onMounted(async () => {
  try {
    await configStore.loadConfig()
  } catch (error) {
    ElMessage.error(`加载配置失败: ${error}`)
  }
})
</script>

<style scoped>
.grading-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.config-card {
  width: 100%;
}

.criteria-container {
  width: 100%;
}

.criteria-hint {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.area-selection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.area-info {
  font-size: 14px;
  color: #666;
}

.operation-card {
  width: 100%;
}

.operation-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.auto-grading-btn {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.auto-grading-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.progress-section {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #666;
}

.image-card {
  width: 100%;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
