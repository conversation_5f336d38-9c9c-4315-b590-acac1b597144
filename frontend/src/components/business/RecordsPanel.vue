<template>
  <div class="records-panel">
    <!-- 操作栏 -->
    <el-card class="toolbar-card" shadow="hover">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="loadRecords" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新记录
          </el-button>
          
          <el-button type="success" @click="handleExport" :disabled="!hasRecords">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
        </div>
        
        <div class="toolbar-right">
          <el-input
            v-model="searchText"
            placeholder="搜索记录..."
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
    </el-card>

    <!-- 记录表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table
        :data="filteredRecords"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="userEmail" label="用户邮箱" width="200" />
        
        <el-table-column label="答案图片" width="120">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="previewImage(row.answerImage)"
            >
              查看图片
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column prop="answerText" label="学生答案" width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.answerText" placement="top">
              <span class="answer-text">{{ truncateText(row.answerText, 50) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="score" label="得分" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getScoreType(row.score)">
              {{ row.score }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="scoreDetails" label="评分详情" width="250">
          <template #default="{ row }">
            <el-tooltip :content="row.scoreDetails" placement="top">
              <span class="score-details">{{ truncateText(row.scoreDetails, 80) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="viewDetails(row)"
            >
              详情
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="deleteRecord(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="showImagePreview"
      title="图片预览"
      width="60%"
      center
    >
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="答案图片" />
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="记录详情"
      width="70%"
    >
      <div v-if="selectedRecord" class="record-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="用户邮箱">{{ selectedRecord.userEmail }}</el-descriptions-item>
          <el-descriptions-item label="得分">
            <el-tag :type="getScoreType(selectedRecord.score)">
              {{ selectedRecord.score }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedRecord.createdAt) }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>学生答案</h4>
          <div class="detail-content">{{ selectedRecord.answerText }}</div>
        </div>

        <div class="detail-section">
          <h4>评分详情</h4>
          <div class="detail-content">{{ selectedRecord.scoreDetails }}</div>
        </div>

        <div class="detail-section">
          <h4>答案图片</h4>
          <div class="detail-image">
            <img :src="selectedRecord.answerImage" alt="答案图片" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, Search } from '@element-plus/icons-vue'
import { useGradingStore } from '@/stores/grading'
import type { GradingRecord } from '@/types'

const gradingStore = useGradingStore()

const loading = ref(false)
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedRecords = ref<GradingRecord[]>([])
const showImagePreview = ref(false)
const previewImageUrl = ref('')
const showDetailsDialog = ref(false)
const selectedRecord = ref<GradingRecord | null>(null)

// 计算属性
const records = computed(() => gradingStore.gradingRecords)
const hasRecords = computed(() => records.value.length > 0)
const totalRecords = computed(() => filteredRecords.value.length)

const filteredRecords = computed(() => {
  if (!searchText.value) return records.value
  
  const search = searchText.value.toLowerCase()
  return records.value.filter(record =>
    record.userEmail.toLowerCase().includes(search) ||
    record.answerText.toLowerCase().includes(search) ||
    record.scoreDetails.toLowerCase().includes(search)
  )
})

// 方法
const loadRecords = async () => {
  try {
    loading.value = true
    await gradingStore.loadGradingRecords()
    ElMessage.success('记录加载成功')
  } catch (error) {
    ElMessage.error(`加载记录失败: ${error}`)
  } finally {
    loading.value = false
  }
}

const handleExport = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中...')
}

const handleSelectionChange = (selection: GradingRecord[]) => {
  selectedRecords.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const viewDetails = (record: GradingRecord) => {
  selectedRecord.value = record
  showDetailsDialog.value = true
}

const deleteRecord = async (record: GradingRecord) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条记录吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用删除API
    ElMessage.success('记录删除成功')
    await loadRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`删除失败: ${error}`)
    }
  }
}

const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'danger'
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadRecords()
})
</script>

<style scoped>
.records-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.answer-text,
.score-details {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.image-preview-container {
  text-align: center;
}

.image-preview-container img {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}

.record-details {
  padding: 20px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.detail-image {
  text-align: center;
}

.detail-image img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
