<template>
  <div class="settings-panel">
    <!-- 系统设置 -->
    <el-card class="settings-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>

      <el-form :model="settings" label-width="150px">
        <el-form-item label="API服务地址">
          <el-input
            v-model="settings.apiUrl"
            placeholder="请输入API服务地址"
          />
        </el-form-item>

        <el-form-item label="默认分析模式">
          <el-select v-model="settings.analysisMode" style="width: 100%">
            <el-option label="标准模式" value="standard" />
            <el-option label="详细模式" value="detailed" />
            <el-option label="简化模式" value="simple" />
          </el-select>
        </el-form-item>

        <el-form-item label="自动保存间隔">
          <el-input-number
            v-model="settings.autoSaveInterval"
            :min="30"
            :max="300"
            :step="30"
            style="width: 200px"
          />
          <span class="unit-text">秒</span>
        </el-form-item>

        <el-form-item label="最大截图宽度">
          <el-input-number
            v-model="settings.maxScreenshotWidth"
            :min="400"
            :max="2000"
            :step="100"
            style="width: 200px"
          />
          <span class="unit-text">像素</span>
        </el-form-item>

        <el-form-item label="启用调试模式">
          <el-switch
            v-model="settings.debugMode"
            active-text="开启"
            inactive-text="关闭"
          />
        </el-form-item>
      </el-form>

      <div class="settings-actions">
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存设置
        </el-button>
        <el-button @click="resetSettings">
          重置为默认
        </el-button>
      </div>
    </el-card>

    <!-- URL配置 -->
    <el-card class="url-config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>URL配置</span>
          <el-button type="primary" size="small" @click="addUrlConfig">
            添加配置
          </el-button>
        </div>
      </template>

      <el-table :data="urlConfigs" stripe style="width: 100%">
        <el-table-column prop="name" label="名称" width="150" />
        <el-table-column prop="url" label="URL" />
        <el-table-column label="操作数量" width="100">
          <template #default="{ row }">
            {{ row.actions?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="editUrlConfig(row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="deleteUrlConfig(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 数据管理 -->
    <el-card class="data-management-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>数据管理</span>
        </div>
      </template>

      <div class="data-actions">
        <el-button type="warning" @click="clearCache">
          <el-icon><Delete /></el-icon>
          清除缓存
        </el-button>

        <el-button type="danger" @click="clearAllData">
          <el-icon><Warning /></el-icon>
          清除所有数据
        </el-button>

        <el-button type="info" @click="exportSettings">
          <el-icon><Download /></el-icon>
          导出设置
        </el-button>

        <el-button type="success" @click="importSettings">
          <el-icon><Upload /></el-icon>
          导入设置
        </el-button>
      </div>

      <div class="data-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="缓存大小">
            {{ formatFileSize(dataInfo.cacheSize) }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库大小">
            {{ formatFileSize(dataInfo.databaseSize) }}
          </el-descriptions-item>
          <el-descriptions-item label="日志文件大小">
            {{ formatFileSize(dataInfo.logSize) }}
          </el-descriptions-item>
          <el-descriptions-item label="总占用空间">
            {{ formatFileSize(dataInfo.totalSize) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 关于信息 -->
    <el-card class="about-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>关于</span>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="应用名称">山竹阅卷</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ appVersion }}</el-descriptions-item>
        <el-descriptions-item label="构建时间">2024-01-15</el-descriptions-item>
        <el-descriptions-item label="技术栈">Vue 3 + Element Plus</el-descriptions-item>
      </el-descriptions>

      <div class="about-actions">
        <el-button type="primary" @click="checkUpdate">
          检查更新
        </el-button>
        <el-button type="info" @click="showAbout">
          关于我们
        </el-button>
      </div>
    </el-card>

    <!-- URL配置对话框 -->
    <el-dialog
      v-model="showUrlConfigDialog"
      :title="editingUrlConfig ? '编辑URL配置' : '添加URL配置'"
      width="60%"
    >
      <el-form :model="urlConfigForm" label-width="100px">
        <el-form-item label="配置名称">
          <el-input v-model="urlConfigForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="URL地址">
          <el-input v-model="urlConfigForm.url" placeholder="请输入URL地址" />
        </el-form-item>
        <el-form-item label="操作配置">
          <div class="actions-config">
            <!-- TODO: 实现操作配置界面 -->
            <p>操作配置功能开发中...</p>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUrlConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveUrlConfig">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Warning, Download, Upload } from '@element-plus/icons-vue'
import { useConfigStore } from '@/stores/config'
import type { ConfigItem } from '@/types'

const configStore = useConfigStore()

const saving = ref(false)
const showUrlConfigDialog = ref(false)
const editingUrlConfig = ref<ConfigItem | null>(null)

const settings = ref({
  apiUrl: '',
  analysisMode: 'standard',
  autoSaveInterval: 60,
  maxScreenshotWidth: 800,
  debugMode: false
})

const urlConfigs = ref<ConfigItem[]>([])

const urlConfigForm = ref({
  name: '',
  url: '',
  actions: []
})

const dataInfo = ref({
  cacheSize: 1024 * 1024 * 5,    // 5MB
  databaseSize: 1024 * 1024 * 10, // 10MB
  logSize: 1024 * 1024 * 2,       // 2MB
  totalSize: 1024 * 1024 * 17     // 17MB
})

// 计算属性
const appVersion = computed(() => 'v0.1.0')

// 方法
const saveSettings = async () => {
  try {
    saving.value = true
    
    // TODO: 调用后端保存设置API
    // await api.saveSettings(settings.value)
    
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error(`保存设置失败: ${error}`)
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  settings.value = {
    apiUrl: '',
    analysisMode: 'standard',
    autoSaveInterval: 60,
    maxScreenshotWidth: 800,
    debugMode: false
  }
  ElMessage.success('设置已重置为默认值')
}

const addUrlConfig = () => {
  editingUrlConfig.value = null
  urlConfigForm.value = {
    name: '',
    url: '',
    actions: []
  }
  showUrlConfigDialog.value = true
}

const editUrlConfig = (config: ConfigItem) => {
  editingUrlConfig.value = config
  urlConfigForm.value = {
    name: config.name,
    url: config.url,
    actions: [...config.actions]
  }
  showUrlConfigDialog.value = true
}

const deleteUrlConfig = async (config: ConfigItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用后端删除配置API
    configStore.removeUrlOption(config.id)
    loadUrlConfigs()
    ElMessage.success('配置删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`删除配置失败: ${error}`)
    }
  }
}

const saveUrlConfig = () => {
  if (!urlConfigForm.value.name || !urlConfigForm.value.url) {
    ElMessage.warning('请填写完整的配置信息')
    return
  }

  const config: ConfigItem = {
    id: editingUrlConfig.value?.id || Date.now().toString(),
    name: urlConfigForm.value.name,
    url: urlConfigForm.value.url,
    actions: urlConfigForm.value.actions
  }

  configStore.addUrlOption(config)
  loadUrlConfigs()
  showUrlConfigDialog.value = false
  ElMessage.success('配置保存成功')
}

const clearCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除缓存吗？这将删除所有临时文件。',
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用后端清除缓存API
    ElMessage.success('缓存清除成功')
    loadDataInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`清除缓存失败: ${error}`)
    }
  }
}

const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '警告：这将删除所有应用数据，包括阅卷记录、配置等。此操作不可恢复！',
      '确认清除所有数据',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    // TODO: 调用后端清除所有数据API
    ElMessage.success('所有数据已清除')
    loadDataInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`清除数据失败: ${error}`)
    }
  }
}

const exportSettings = () => {
  // TODO: 实现导出设置功能
  ElMessage.info('导出设置功能开发中...')
}

const importSettings = () => {
  // TODO: 实现导入设置功能
  ElMessage.info('导入设置功能开发中...')
}

const checkUpdate = () => {
  // TODO: 实现检查更新功能
  ElMessage.info('当前已是最新版本')
}

const showAbout = () => {
  // TODO: 显示关于对话框
  ElMessage.info('关于对话框开发中...')
}

const loadUrlConfigs = () => {
  urlConfigs.value = configStore.urlOptions
}

const loadDataInfo = async () => {
  try {
    // TODO: 调用后端获取数据信息API
    // const info = await api.getDataInfo()
    // dataInfo.value = info
  } catch (error) {
    ElMessage.error(`加载数据信息失败: ${error}`)
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(async () => {
  await configStore.loadConfig()
  loadUrlConfigs()
  loadDataInfo()
})
</script>

<style scoped>
.settings-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.unit-text {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.settings-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.data-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.data-info {
  margin-top: 20px;
}

.about-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.actions-config {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  text-align: center;
  color: #666;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
