<template>
  <div class="export-panel">
    <!-- 导出配置 -->
    <el-card class="config-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>导出配置</span>
        </div>
      </template>

      <el-form :model="exportConfig" label-width="120px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportConfig.format">
            <el-radio label="excel">Excel (.xlsx)</el-radio>
            <el-radio label="csv">CSV (.csv)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportConfig.fields">
            <el-checkbox label="id">记录ID</el-checkbox>
            <el-checkbox label="userEmail">用户邮箱</el-checkbox>
            <el-checkbox label="answerText">学生答案</el-checkbox>
            <el-checkbox label="score">得分</el-checkbox>
            <el-checkbox label="scoreDetails">评分详情</el-checkbox>
            <el-checkbox label="createdAt">创建时间</el-checkbox>
            <el-checkbox label="answerImage">答案图片</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="exportConfig.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="导出目录">
          <div class="directory-selector">
            <el-input
              v-model="exportConfig.directory"
              placeholder="选择导出目录..."
              readonly
            />
            <el-button type="primary" @click="selectDirectory">
              选择目录
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="包含分析报告">
          <el-switch
            v-model="exportConfig.includeAnalysis"
            active-text="是"
            inactive-text="否"
          />
          <span class="help-text">生成AI分析报告并保存为Markdown文件</span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 导出操作 -->
    <el-card class="operation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>导出操作</span>
        </div>
      </template>

      <div class="export-actions">
        <el-button
          type="primary"
          size="large"
          @click="handleExport"
          :loading="exporting"
          :disabled="!canExport"
        >
          <el-icon><Download /></el-icon>
          开始导出
        </el-button>

        <el-button
          type="success"
          size="large"
          @click="handleQuickExport"
          :loading="exporting"
        >
          <el-icon><DocumentCopy /></el-icon>
          快速导出 (Excel)
        </el-button>
      </div>

      <!-- 导出进度 -->
      <div v-if="exporting" class="export-progress">
        <el-progress
          :percentage="exportProgress"
          :status="exportProgress === 100 ? 'success' : undefined"
        />
        <p class="progress-text">{{ exportStatusText }}</p>
      </div>
    </el-card>

    <!-- 导出历史 -->
    <el-card class="history-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>导出历史</span>
          <el-button type="text" @click="loadExportHistory">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="exportHistory" stripe style="width: 100%">
        <el-table-column prop="filename" label="文件名" />
        <el-table-column prop="format" label="格式" width="80" />
        <el-table-column prop="recordCount" label="记录数" width="100" />
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="导出时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="openFile(row.filePath)"
            >
              打开
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: #f56c6c"
              @click="deleteExportFile(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, DocumentCopy, Refresh } from '@element-plus/icons-vue'
import { useGradingStore } from '@/stores/grading'

const gradingStore = useGradingStore()

const exporting = ref(false)
const exportProgress = ref(0)
const exportStatusText = ref('')

const exportConfig = ref({
  format: 'excel',
  fields: ['id', 'userEmail', 'answerText', 'score', 'scoreDetails', 'createdAt'],
  dateRange: null as [string, string] | null,
  directory: '',
  includeAnalysis: false
})

const exportHistory = ref([
  // 模拟数据
  {
    filename: 'grading_records_2024_01_15.xlsx',
    format: 'Excel',
    recordCount: 150,
    fileSize: 2048576,
    createdAt: '2024-01-15 14:30:00',
    filePath: '/path/to/file.xlsx'
  }
])

// 计算属性
const canExport = computed(() => {
  return exportConfig.value.fields.length > 0 && exportConfig.value.directory
})

// 方法
const selectDirectory = async () => {
  try {
    // TODO: 调用后端选择目录API
    // const directory = await api.selectDirectory()
    // exportConfig.value.directory = directory
    
    // 模拟选择目录
    exportConfig.value.directory = '/Users/<USER>/Documents/exports'
    ElMessage.success('目录选择成功')
  } catch (error) {
    ElMessage.error(`选择目录失败: ${error}`)
  }
}

const handleExport = async () => {
  try {
    exporting.value = true
    exportProgress.value = 0
    exportStatusText.value = '准备导出...'

    // 模拟导出进度
    const progressInterval = setInterval(() => {
      if (exportProgress.value < 90) {
        exportProgress.value += 10
        updateStatusText()
      }
    }, 500)

    // TODO: 调用后端导出API
    // await api.exportRecords(exportConfig.value)

    // 模拟导出完成
    setTimeout(() => {
      clearInterval(progressInterval)
      exportProgress.value = 100
      exportStatusText.value = '导出完成'
      ElMessage.success('导出成功')
      loadExportHistory()
    }, 5000)

  } catch (error) {
    ElMessage.error(`导出失败: ${error}`)
  } finally {
    setTimeout(() => {
      exporting.value = false
      exportProgress.value = 0
      exportStatusText.value = ''
    }, 2000)
  }
}

const handleQuickExport = async () => {
  const quickConfig = {
    format: 'excel',
    fields: ['id', 'userEmail', 'answerText', 'score', 'scoreDetails', 'createdAt'],
    directory: exportConfig.value.directory || '/Users/<USER>/Documents',
    includeAnalysis: false
  }

  try {
    exporting.value = true
    exportStatusText.value = '快速导出中...'

    // TODO: 调用后端快速导出API
    // await api.quickExport(quickConfig)

    ElMessage.success('快速导出成功')
    loadExportHistory()
  } catch (error) {
    ElMessage.error(`快速导出失败: ${error}`)
  } finally {
    exporting.value = false
    exportStatusText.value = ''
  }
}

const loadExportHistory = async () => {
  try {
    // TODO: 调用后端获取导出历史API
    // const history = await api.getExportHistory()
    // exportHistory.value = history
  } catch (error) {
    ElMessage.error(`加载导出历史失败: ${error}`)
  }
}

const openFile = async (filePath: string) => {
  try {
    // TODO: 调用后端打开文件API
    // await api.openFile(filePath)
    ElMessage.success('文件已打开')
  } catch (error) {
    ElMessage.error(`打开文件失败: ${error}`)
  }
}

const deleteExportFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.filename}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用后端删除文件API
    // await api.deleteExportFile(file.filePath)

    ElMessage.success('文件删除成功')
    loadExportHistory()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`删除文件失败: ${error}`)
    }
  }
}

const updateStatusText = () => {
  const texts = [
    '正在查询记录...',
    '正在处理数据...',
    '正在生成文件...',
    '正在保存文件...',
    '正在生成分析报告...'
  ]
  const index = Math.floor(exportProgress.value / 20)
  exportStatusText.value = texts[index] || '导出中...'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadExportHistory()
})
</script>

<style scoped>
.export-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.directory-selector {
  display: flex;
  gap: 12px;
}

.directory-selector .el-input {
  flex: 1;
}

.help-text {
  font-size: 12px;
  color: #999;
  margin-left: 12px;
}

.export-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.export-progress {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}
</style>
