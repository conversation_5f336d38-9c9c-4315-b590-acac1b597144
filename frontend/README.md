# 山竹阅卷系统 - 前端

基于 Vue 3 + TypeScript + Element Plus 的智能阅卷系统前端应用。

## 🚀 技术栈

- **框架**: Vue 3.3+ (Composition API)
- **语言**: TypeScript 5.0+
- **UI库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **构建工具**: Vite 5.0+
- **测试**: Vitest + Vue Test Utils
- **代码规范**: ESLint + Prettier

## 📦 项目结构

```text
frontend/
├── src/
│   ├── components/          # 组件
│   │   ├── business/        # 业务组件
│   │   └── common/          # 通用组件
│   ├── stores/              # Pinia 状态管理
│   ├── views/               # 页面组件
│   ├── router/              # 路由配置
│   ├── types/               # TypeScript 类型定义
│   ├── utils/               # 工具函数
│   └── tests/               # 测试文件
├── public/                  # 静态资源
└── dist/                    # 构建输出
```

## 🛠️ 开发环境

### 环境要求

- Node.js 18.0+
- npm 9.0+

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🧪 测试

### 运行所有测试

```bash
npm run test
```

### 运行测试 UI

```bash
npm run test:ui
```

### 生成测试覆盖率报告

```bash
npm run test:coverage
```

## 📋 代码规范

### 代码检查

```bash
npm run lint
```

### 代码格式化

```bash
npm run format
```

## 🏗️ 架构设计

### 组件架构

- **业务组件** (`components/business/`): 特定业务逻辑的组件
- **通用组件** (`components/common/`): 可复用的基础组件
- **页面组件** (`views/`): 路由对应的页面级组件

### 状态管理

使用 Pinia 进行状态管理，按功能模块划分：

- `auth.ts`: 用户认证状态
- `grading.ts`: 阅卷相关状态
- `config.ts`: 系统配置状态

### 路由设计

- `/login`: 登录页面
- `/home`: 主页面（包含所有功能模块）

## 🔧 核心功能

### 1. 用户认证

- 登录/登出
- 记住登录状态
- 密码重置

### 2. 智能阅卷

- 配置阅卷参数
- 自动阅卷流程
- 进度监控
- 结果展示

### 3. 记录管理

- 阅卷记录查看
- 记录搜索和筛选
- 记录详情查看

### 4. 数据导出

- Excel/CSV 格式导出
- 自定义导出字段
- 导出历史管理

### 5. 系统设置

- 基础配置管理
- URL 配置
- 数据清理

## 🎨 UI 设计原则

### 设计系统

- 使用 Element Plus 设计语言
- 保持界面简洁清晰
- 响应式设计
- 无障碍访问支持

### 颜色方案

- 主色调：蓝色系 (#409EFF)
- 成功色：绿色系 (#67C23A)
- 警告色：橙色系 (#E6A23C)
- 危险色：红色系 (#F56C6C)

### 布局规范

- 统一的间距系统 (8px 基准)
- 一致的组件高度
- 清晰的视觉层次

## 🔍 性能优化

### 代码分割

- 路由级别的懒加载
- 组件级别的异步加载

### 缓存策略

- 组件缓存
- API 响应缓存
- 静态资源缓存

### 性能监控

- 组件渲染性能监控
- 内存使用监控
- 用户交互性能追踪

## 🐛 调试指南

### 开发工具

- Vue DevTools
- Chrome DevTools
- Vite DevTools

### 日志系统

- 开发环境：控制台日志
- 生产环境：错误上报

### 常见问题

1. **组件不渲染**: 检查 props 类型和响应式数据
2. **状态不更新**: 确认 Pinia store 的正确使用
3. **路由跳转失败**: 检查路由配置和权限

## 📚 开发规范

### 命名规范

- **组件**: PascalCase (如 `LoginView.vue`)
- **文件**: kebab-case (如 `user-profile.ts`)
- **变量**: camelCase (如 `userName`)
- **常量**: UPPER_SNAKE_CASE (如 `API_BASE_URL`)

### 代码组织

- 每个文件不超过 300 行
- 函数不超过 50 行
- 合理使用注释
- 保持代码简洁

### Git 提交规范

```text
type(scope): description

feat: 新功能
fix: 修复
docs: 文档
style: 格式
refactor: 重构
test: 测试
chore: 构建
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request
