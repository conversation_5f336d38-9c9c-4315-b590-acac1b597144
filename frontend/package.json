{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue/runtime-core": "^3.3.8", "element-plus": "^2.4.2", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.9.0", "@vitejs/plugin-vue": "^4.5.0", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.4.0", "eslint": "^9.35.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^3.2.4", "vue-tsc": "^1.8.22"}}