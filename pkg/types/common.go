package types

import "time"

// AreaCoords 表示页面区域坐标
type AreaCoords struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// ConfigItem 表示配置项
type ConfigItem struct {
	ID      string   `json:"id"`
	Name    string   `json:"name"`
	URL     string   `json:"url"`
	Actions []Action `json:"actions"`
}

// Action 表示操作配置
type Action struct {
	Type     string `json:"type"` // click, fill, screenshot
	Selector string `json:"selector"`
	Index    int    `json:"index"`
	Value    string `json:"value,omitempty"`
}

// GradingCriteriaStruct 结构化评分标准
type GradingCriteriaStruct struct {
	DefaultCriteria string `json:"default_criteria" yaml:"default_criteria" mapstructure:"default_criteria"`
	ScoringPoints   string `json:"scoring_points" yaml:"scoring_points" mapstructure:"scoring_points"`
	DeductionPoints string `json:"deduction_points" yaml:"deduction_points" mapstructure:"deduction_points"`
	TotalScore      int    `json:"total_score" yaml:"total_score" mapstructure:"total_score"`
}

// GradingResponse 评分响应
type GradingResponse struct {
	StudentAnswer  string `json:"student_answer"`
	Score          int    `json:"score"`
	GradingDetails string `json:"grading_details"`
	Balance        int    `json:"balance,omitempty"`
}

// GradingCriteria 评分标准数据库模型
type GradingCriteria struct {
	ID        string    `gorm:"primaryKey" json:"id"`
	Content   string    `gorm:"not null" json:"content"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// GradingRecord 阅卷记录数据库模型
type GradingRecord struct {
	ID                uint            `gorm:"primaryKey" json:"id"`
	UserEmail         string          `gorm:"not null" json:"user_email"`
	AnswerImage       string          `gorm:"not null" json:"answer_image"`
	AnswerImageHash   string          `gorm:"not null;index" json:"answer_image_hash"`
	GradingCriteriaID string          `gorm:"not null;index" json:"grading_criteria_id"`
	GradingCriteria   GradingCriteria `gorm:"foreignKey:GradingCriteriaID" json:"-"`
	AnswerText        string          `gorm:"not null" json:"answer_text"`
	Score             float64         `gorm:"not null" json:"score"`
	ScoreDetails      string          `gorm:"not null" json:"score_details"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`
}

// ElementOperation 元素操作
type ElementOperation struct {
	Type        string `json:"type"` // click, input
	Selector    string `json:"selector"`
	Value       string `json:"value,omitempty"`
	Description string `json:"description"`
}
