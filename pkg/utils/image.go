package utils

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"image"
	"image/jpeg"

	"golang.org/x/image/draw"
)

// ResizeImage 调整图片大小
func ResizeImage(imageData []byte, maxWidth int) ([]byte, string, error) {
	if maxWidth <= 0 {
		return imageData, "png", nil
	}

	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, "", fmt.Errorf("failed to decode image: %v", err)
	}

	// 获取原始尺寸
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// 如果原始宽度小于等于最大宽度，直接返回
	if originalWidth <= maxWidth {
		return imageData, format, nil
	}

	// 计算新的尺寸，保持宽高比
	newWidth := maxWidth
	newHeight := int(float64(originalHeight) * float64(maxWidth) / float64(originalWidth))

	// 创建新的图片
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 缩放图片
	draw.CatmullRom.Scale(newImg, newImg.Bounds(), img, bounds, draw.Over, nil)

	// 编码为JPEG以减小文件大小
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, newImg, &jpeg.Options{Quality: 85})
	if err != nil {
		return nil, "", fmt.Errorf("failed to encode image: %v", err)
	}

	return buf.Bytes(), "jpeg", nil
}

// ImageToBase64 将图片数据转换为base64字符串
func ImageToBase64(imageData []byte, format string) string {
	mimeType := "image/png"
	if format == "jpeg" || format == "jpg" {
		mimeType = "image/jpeg"
	}

	base64Data := base64.StdEncoding.EncodeToString(imageData)
	return fmt.Sprintf("data:%s;base64,%s", mimeType, base64Data)
}

// ProcessScreenshot 处理截图数据
func ProcessScreenshot(imageData []byte, maxWidth int) (string, error) {
	// 压缩图片
	processedBytes, format, err := ResizeImage(imageData, maxWidth)
	if err != nil {
		// 如果压缩失败，使用原图
		processedBytes = imageData
		format = "png"
	}

	// 转换为base64
	return ImageToBase64(processedBytes, format), nil
}
