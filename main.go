package main

import (
	"context"
	"embed"

	"ai-grading/internal/app"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
	"github.com/wailsapp/wails/v2/pkg/options/mac"
	"github.com/wailsapp/wails/v2/pkg/options/windows"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

//go:embed all:frontend/dist
var assets embed.FS

var wailsContext *context.Context

func onSecondInstanceLaunch(secondInstanceData options.SecondInstanceData) {
	secondInstanceArgs := secondInstanceData.Args
	rt.WindowUnminimise(*wailsContext)
	rt.Show(*wailsContext)
	go rt.EventsEmit(*wailsContext, "launchArgs", secondInstanceArgs)
}

func main() {
	appInstance := app.NewApp()

	err := wails.Run(&options.App{
		Title:       "山竹阅卷",
		MinWidth:    630,
		Width:       630,
		MinHeight:   760,
		Height:      760,
		AlwaysOnTop: true,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		Mac: &mac.Options{
			TitleBar: mac.TitleBarDefault(),
			About: &mac.AboutInfo{
				Title:   "山竹阅卷",
				Message: "© 2025 ShanZhuLab. All rights reserved.",
				Icon:    nil,
			},
		},
		Windows: &windows.Options{
			WebviewIsTransparent: true,
			BackdropType:         windows.Acrylic,
			WindowIsTranslucent:  false,
			DisableWindowIcon:    false,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup: func(ctx context.Context) {
			wailsContext = &ctx
			appInstance.Startup(ctx)
		},
		OnShutdown: func(ctx context.Context) {
			appInstance.Shutdown(ctx)
		},
		SingleInstanceLock: &options.SingleInstanceLock{
			UniqueId:               "2ebf39c2-915a-464e-a978-e98c8acc9931",
			OnSecondInstanceLaunch: onSecondInstanceLaunch,
		},
		Bind: []any{
			appInstance,
		},
	})
	if err != nil {
		println("Error:", err.Error())
	}
}
