# 山竹阅卷系统重构设计和实现流程说明书

## 1. 项目概述

### 1.1 重构目标
将现有的基于 React + Ant Design + Playwright 的山竹阅卷系统重构为基于 Vue 3 + TypeScript + Element Plus + ChromeDP 的新架构，提升代码的模块化程度和可维护性。

### 1.2 技术栈对比

| 组件 | 当前技术栈 | 新技术栈 |
|------|-----------|----------|
| 前端框架 | React 18 | Vue 3 + TypeScript |
| UI组件库 | Ant Design | Element Plus |
| 状态管理 | React Context | Pinia |
| 构建工具 | Vite | Vite |
| 浏览器自动化 | Playwright | ChromeDP |
| 后端框架 | Wails v2 + Go | Wails v2 + Go |
| 数据库 | SQLite + GORM | SQLite + GORM |

## 2. 架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端层 (Vue 3 + TypeScript)"
        A[Vue 3 应用]
        B[Element Plus UI]
        C[Pinia 状态管理]
        D[TypeScript 类型系统]
    end
    
    subgraph "Wails 桥接层"
        E[Wails Runtime]
        F[Go 方法绑定]
    end
    
    subgraph "后端服务层 (Go)"
        G[应用核心服务]
        H[浏览器自动化服务]
        I[数据库服务]
        J[配置管理服务]
        K[日志服务]
    end
    
    subgraph "浏览器自动化层"
        L[ChromeDP]
        M[Chrome 浏览器]
    end
    
    subgraph "数据存储层"
        N[SQLite 数据库]
        O[配置文件]
        P[日志文件]
    end
    
    A --> E
    B --> A
    C --> A
    D --> A
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    H --> L
    L --> M
    I --> N
    J --> O
    K --> P
```

### 2.2 模块化设计原则

#### 2.2.1 高内聚原则
- **功能模块内聚**：相关功能集中在同一模块内
- **数据内聚**：相关数据结构和操作封装在一起
- **接口内聚**：模块对外提供清晰统一的接口

#### 2.2.2 低耦合原则
- **依赖注入**：通过接口而非具体实现进行依赖
- **事件驱动**：模块间通过事件进行松耦合通信
- **配置驱动**：通过配置文件控制模块行为

#### 2.2.3 清晰命名原则
- **语义化命名**：函数、变量、类型名称能够清晰表达其用途
- **一致性命名**：同类功能使用一致的命名模式
- **层次化命名**：通过命名空间体现模块层次关系

## 3. 前端重构设计

### 3.1 项目结构设计

```
frontend/
├── src/
│   ├── components/           # 通用组件
│   │   ├── common/          # 基础通用组件
│   │   ├── business/        # 业务组件
│   │   └── layout/          # 布局组件
│   ├── views/               # 页面视图
│   │   ├── login/           # 登录页面
│   │   ├── home/            # 主页面
│   │   └── grading/         # 阅卷相关页面
│   ├── stores/              # Pinia 状态管理
│   │   ├── auth.ts          # 认证状态
│   │   ├── grading.ts       # 阅卷状态
│   │   └── config.ts        # 配置状态
│   ├── composables/         # 组合式函数
│   │   ├── useAuth.ts       # 认证逻辑
│   │   ├── useGrading.ts    # 阅卷逻辑
│   │   └── useBrowser.ts    # 浏览器操作逻辑
│   ├── types/               # TypeScript 类型定义
│   │   ├── api.ts           # API 相关类型
│   │   ├── grading.ts       # 阅卷相关类型
│   │   └── common.ts        # 通用类型
│   ├── utils/               # 工具函数
│   │   ├── request.ts       # 请求工具
│   │   ├── format.ts        # 格式化工具
│   │   └── validation.ts    # 验证工具
│   ├── assets/              # 静态资源
│   ├── router/              # 路由配置
│   └── main.ts              # 应用入口
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 3.2 状态管理设计 (Pinia)

#### 3.2.1 认证状态管理
```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: false,
    userInfo: null as UserInfo | null,
    token: '',
    config: null as Config | null
  }),
  
  actions: {
    async login(credentials: LoginCredentials),
    async logout(),
    async refreshToken(),
    updateConfig(config: Config)
  }
})
```

#### 3.2.2 阅卷状态管理
```typescript
// stores/grading.ts
export const useGradingStore = defineStore('grading', {
  state: () => ({
    isAutoGrading: false,
    currentImage: '',
    gradingCriteria: '',
    selectedUrl: '',
    selectedAreaCoords: null as AreaCoords | null,
    gradingRecords: [] as GradingRecord[]
  }),
  
  actions: {
    async startAutoGrading(),
    async stopAutoGrading(),
    async fetchImage(),
    async submitGrade(score: number)
  }
})
```

### 3.3 组件设计

#### 3.3.1 组件分类
- **基础组件**：按钮、输入框、对话框等通用UI组件
- **业务组件**：阅卷相关的专用组件
- **布局组件**：页面布局和导航组件

#### 3.3.2 组件命名规范
- 使用 PascalCase 命名
- 组件文件名与组件名保持一致
- 业务组件添加业务前缀，如 `GradingPanel`

## 4. 后端重构设计

### 4.1 模块化架构

```
backend/
├── internal/
│   ├── app/                 # 应用核心
│   │   ├── app.go          # 应用主结构
│   │   └── lifecycle.go    # 生命周期管理
│   ├── browser/            # 浏览器自动化模块
│   │   ├── chromedp.go     # ChromeDP 实现
│   │   ├── operations.go   # 浏览器操作
│   │   └── screenshot.go   # 截图功能
│   ├── grading/            # 阅卷业务模块
│   │   ├── service.go      # 阅卷服务
│   │   ├── criteria.go     # 评分标准
│   │   └── record.go       # 阅卷记录
│   ├── config/             # 配置管理模块
│   │   ├── config.go       # 配置结构
│   │   └── manager.go      # 配置管理器
│   ├── database/           # 数据库模块
│   │   ├── db.go           # 数据库连接
│   │   └── models.go       # 数据模型
│   └── logger/             # 日志模块
│       ├── logger.go       # 日志器
│       └── config.go       # 日志配置
├── pkg/                    # 公共包
│   ├── types/              # 类型定义
│   ├── utils/              # 工具函数
│   └── errors/             # 错误定义
└── main.go                 # 应用入口
```

### 4.2 ChromeDP 集成设计

#### 4.2.1 浏览器管理器
```go
type BrowserManager struct {
    ctx    context.Context
    cancel context.CancelFunc
    
    // ChromeDP 相关
    allocCtx context.Context
    taskCtx  context.Context
}

func (bm *BrowserManager) Start() error
func (bm *BrowserManager) Stop() error
func (bm *BrowserManager) Navigate(url string) error
func (bm *BrowserManager) Screenshot(selector string) ([]byte, error)
func (bm *BrowserManager) Click(selector string) error
func (bm *BrowserManager) Fill(selector, value string) error
```

#### 4.2.2 操作抽象层
```go
type BrowserOperator interface {
    Navigate(url string) error
    Screenshot(selector string) ([]byte, error)
    ScreenshotArea(x, y, width, height int) ([]byte, error)
    Click(selector string) error
    Fill(selector, value string) error
    WaitForElement(selector string, timeout time.Duration) error
}
```

### 4.3 服务接口设计

#### 4.3.1 阅卷服务接口
```go
type GradingService interface {
    StartAutoGrading(criteria string, paperCount int) error
    StopAutoGrading() error
    FetchImage(urlName string) (string, error)
    SubmitGrade(urlName string, score string) error
    GetGradingRecords() ([]GradingRecord, error)
}
```

#### 4.3.2 配置服务接口
```go
type ConfigService interface {
    GetConfig() (*Config, error)
    SaveConfig(config *Config) error
    GetGradingCriteria() (*GradingCriteria, error)
    SaveGradingCriteria(criteria *GradingCriteria) error
}
```

## 5. 实现流程

### 5.1 第一阶段：环境准备和基础架构

#### 5.1.1 创建新分支
```bash
git checkout -b refactor/vue-and-chromedp
```

#### 5.1.2 前端环境搭建
1. 更新 `frontend/package.json`，替换依赖
2. 配置 Vue 3 + TypeScript + Element Plus
3. 设置 Vite 配置
4. 配置 ESLint 和 Prettier

#### 5.1.3 后端模块重构
1. 重构项目结构，按模块组织代码
2. 实现 ChromeDP 浏览器管理器
3. 重构现有服务，提取接口

### 5.2 第二阶段：核心功能迁移

#### 5.2.1 浏览器自动化迁移
1. 实现 ChromeDP 版本的浏览器操作
2. 迁移截图功能
3. 迁移元素操作功能
4. 测试浏览器自动化功能

#### 5.2.2 前端页面重构
1. 实现登录页面 (Vue + Element Plus)
2. 实现主页面布局
3. 实现阅卷功能页面
4. 配置路由和状态管理

### 5.3 第三阶段：业务功能完善

#### 5.3.1 阅卷功能完善
1. 实现自动阅卷流程
2. 实现评分标准管理
3. 实现阅卷记录管理
4. 实现导出功能

#### 5.3.2 配置和设置
1. 实现配置管理界面
2. 实现用户设置
3. 实现系统设置

### 5.4 第四阶段：测试和优化

#### 5.4.1 功能测试
1. 单元测试
2. 集成测试
3. 端到端测试

#### 5.4.2 性能优化
1. 前端性能优化
2. 后端性能优化
3. 内存使用优化

## 6. 风险评估和应对策略

### 6.1 技术风险
- **ChromeDP 兼容性**：可能存在与 Playwright 行为差异
- **Vue 3 学习曲线**：团队需要适应新的开发模式

### 6.2 应对策略
- 逐步迁移，保持功能对等
- 充分测试，确保功能正确性
- 保留原有分支作为备份

## 7. 时间规划

| 阶段 | 预计时间 | 主要任务 |
|------|----------|----------|
| 第一阶段 | 1-2 周 | 环境准备和基础架构 |
| 第二阶段 | 2-3 周 | 核心功能迁移 |
| 第三阶段 | 2-3 周 | 业务功能完善 |
| 第四阶段 | 1-2 周 | 测试和优化 |

总计：6-10 周

## 8. 详细技术实现

### 8.1 ChromeDP 实现细节

#### 8.1.1 浏览器管理器实现
```go
// internal/browser/manager.go
package browser

import (
    "context"
    "time"

    "github.com/chromedp/chromedp"
)

type Manager struct {
    allocCtx context.Context
    taskCtx  context.Context
    cancel   context.CancelFunc
}

func NewManager() *Manager {
    return &Manager{}
}

func (m *Manager) Start() error {
    // 创建 Chrome 实例
    opts := append(chromedp.DefaultExecAllocatorOptions[:],
        chromedp.Flag("headless", false),
        chromedp.Flag("disable-gpu", false),
        chromedp.Flag("disable-dev-shm-usage", true),
        chromedp.Flag("no-sandbox", true),
    )

    m.allocCtx, m.cancel = chromedp.NewExecAllocator(context.Background(), opts...)
    m.taskCtx, _ = chromedp.NewContext(m.allocCtx)

    return chromedp.Run(m.taskCtx)
}

func (m *Manager) Stop() error {
    if m.cancel != nil {
        m.cancel()
    }
    return nil
}

func (m *Manager) Navigate(url string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.Navigate(url),
        chromedp.WaitReady("body"),
    )
}

func (m *Manager) Screenshot(selector string) ([]byte, error) {
    var buf []byte
    err := chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Screenshot(selector, &buf, chromedp.NodeVisible),
    )
    return buf, err
}

func (m *Manager) Click(selector string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Click(selector),
    )
}

func (m *Manager) Fill(selector, value string) error {
    return chromedp.Run(m.taskCtx,
        chromedp.WaitVisible(selector),
        chromedp.Clear(selector),
        chromedp.SendKeys(selector, value),
    )
}
```

#### 8.1.2 操作抽象层
```go
// internal/browser/operations.go
package browser

import (
    "context"
    "time"
)

type Operations struct {
    manager *Manager
}

func NewOperations(manager *Manager) *Operations {
    return &Operations{manager: manager}
}

func (o *Operations) ScreenshotArea(x, y, width, height int) ([]byte, error) {
    var buf []byte
    err := chromedp.Run(o.manager.taskCtx,
        chromedp.Screenshot(&buf, chromedp.ByID(""), chromedp.FromNode(&chromedp.Node{
            // 实现区域截图逻辑
        })),
    )
    return buf, err
}

func (o *Operations) WaitForElement(selector string, timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(o.manager.taskCtx, timeout)
    defer cancel()

    return chromedp.Run(ctx,
        chromedp.WaitVisible(selector),
    )
}
```

### 8.2 Vue 3 前端实现

#### 8.2.1 主应用配置
```typescript
// src/main.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

const app = createApp(App)
const pinia = createPinia()

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

app.mount('#app')
```

#### 8.2.2 类型定义
```typescript
// src/types/grading.ts
export interface GradingCriteria {
  id: string
  content: string
  defaultCriteria: string
  scoringPoints: string
  deductionPoints: string
  totalScore: number
}

export interface GradingRecord {
  id: number
  userEmail: string
  answerImage: string
  answerText: string
  score: number
  scoreDetails: string
  createdAt: string
}

export interface AreaCoords {
  x: number
  y: number
  width: number
  height: number
}

export interface ConfigItem {
  id: string
  name: string
  url: string
  actions: Action[]
}

export interface Action {
  type: 'click' | 'fill' | 'screenshot'
  selector: string
  index: number
  value?: string
}
```

#### 8.2.3 Composables 实现
```typescript
// src/composables/useGrading.ts
import { ref, computed } from 'vue'
import { useGradingStore } from '@/stores/grading'
import { ElMessage } from 'element-plus'

export function useGrading() {
  const store = useGradingStore()
  const loading = ref(false)

  const isAutoGrading = computed(() => store.isAutoGrading)
  const currentImage = computed(() => store.currentImage)

  const startAutoGrading = async (paperCount: number) => {
    try {
      loading.value = true
      await store.startAutoGrading(paperCount)
      ElMessage.success('自动阅卷已开始')
    } catch (error) {
      ElMessage.error(`启动失败: ${error}`)
    } finally {
      loading.value = false
    }
  }

  const stopAutoGrading = async () => {
    try {
      await store.stopAutoGrading()
      ElMessage.success('自动阅卷已停止')
    } catch (error) {
      ElMessage.error(`停止失败: ${error}`)
    }
  }

  const fetchImage = async () => {
    try {
      loading.value = true
      await store.fetchImage()
    } catch (error) {
      ElMessage.error(`抓图失败: ${error}`)
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    isAutoGrading,
    currentImage,
    startAutoGrading,
    stopAutoGrading,
    fetchImage
  }
}
```

### 8.3 依赖管理

#### 8.3.1 Go 依赖更新
```go
// go.mod 需要添加的依赖
require (
    github.com/chromedp/chromedp v0.9.3
    github.com/chromedp/cdproto v0.0.0-20231011050154-1d073bb38998
)

// 移除的依赖
// github.com/playwright-community/playwright-go v0.5200.0
```

#### 8.3.2 前端依赖配置
```json
{
  "dependencies": {
    "vue": "^3.3.8",
    "@vue/runtime-core": "^3.3.8",
    "element-plus": "^2.4.2",
    "@element-plus/icons-vue": "^2.1.0",
    "pinia": "^2.1.7",
    "vue-router": "^4.2.5"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/tsconfig": "^0.4.0",
    "typescript": "^5.2.2",
    "vite": "^5.0.0",
    "vue-tsc": "^1.8.22"
  }
}
```

## 9. 迁移检查清单

### 9.1 后端迁移检查
- [ ] ChromeDP 浏览器管理器实现
- [ ] 截图功能迁移 (ScreenshotElement -> ChromeDP)
- [ ] 元素操作迁移 (Click, Fill -> ChromeDP)
- [ ] 页面导航迁移 (Navigate -> ChromeDP)
- [ ] 区域截图功能实现
- [ ] 自动阅卷流程适配
- [ ] 错误处理和日志记录
- [ ] 配置管理兼容性

### 9.2 前端迁移检查
- [ ] Vue 3 项目结构搭建
- [ ] Element Plus 组件替换
- [ ] Pinia 状态管理实现
- [ ] TypeScript 类型定义
- [ ] 路由配置
- [ ] 登录页面重构
- [ ] 主页面重构
- [ ] 阅卷功能页面重构
- [ ] 对话框组件重构
- [ ] 工具函数迁移

### 9.3 功能验证检查
- [ ] 用户登录功能
- [ ] 浏览器打开和导航
- [ ] 页面截图功能
- [ ] 区域选择和截图
- [ ] 自动阅卷流程
- [ ] 评分标准管理
- [ ] 阅卷记录查看
- [ ] 数据导出功能
- [ ] 配置管理
- [ ] 日志记录

## 10. 成功标准

1. 所有现有功能在新架构下正常工作
2. 代码结构更加清晰，模块化程度更高
3. 性能不低于原有系统
4. 代码可维护性显著提升
5. 通过所有测试用例
6. ChromeDP 替换 Playwright 无功能缺失
7. Vue 3 + Element Plus 界面美观易用
8. TypeScript 类型安全得到保障
