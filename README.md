# README

## About

This is the official Wails React-TS template.

You can configure the project by editing `wails.json`. More information about the project settings can be found here:
[https://wails.io/docs/reference/project-config](https://wails.io/docs/reference/project-config)

## Live Development

To run in live development mode, run `wails dev` in the project directory. This will run a Vite development server that will
provide very fast hot reload of your frontend changes. If you want to develop in a browser and have access to your Go methods,
there is also a dev server that runs on [http://localhost:34115](http://localhost:34115). Connect to this in your browser, and you can call your Go code
from devtools.

## Building

To build a redistributable, production mode package, use `wails build -clean -upx`(macos不支持-upx).

Wails includes support for obfuscating your application using garble.

To produce an obfuscated build, you can use the -obfuscate flag with the wails build command:

`wails build -obfuscated`

To customise the obfuscation settings, you can use the -garbleargs flag:

使用 `-literals` 标志会导致文字表达式（例如字符串）被更复杂的表达式替换，并在运行时解析为相同的值。通过 `-ldflags=-X` 注入的字符串文字也将被此标志替换。此功能是可选的，因为它可能会导致速度变慢，具体取决于输入代码。

使用 `-tiny` 标志，Go 二进制文件中会删除更多信息。位置信息会被完全删除，而不是被混淆。打印 panic、致命错误和跟踪/调试信息的运行时代码会被删除。

wails build -obfuscated -garbleargs "-literals -tiny -seed=myrandomseed"

## 架构流程图

```mermaid
sequenceDiagram
    participant Wails as Wails客户端
    participant TOS as 火山TOS
    participant SCF as 腾讯云函数
    participant AI as 火山方舟AI

    Wails->>TOS: 1. 上传截图（内网加速）
    TOS-->>Wails: 返回tos://地址
    Wails->>SCF: 2. 调用云函数（携带tos地址）
    SCF->>AI: 3. 请求AI评分（内网传tos地址）
    AI->>TOS: 4. 内网读取图片（0费用）
    AI-->>SCF: 返回评分结果
    SCF-->>Wails: 5. 返回评分结果
```

## 刷新Token核心流程

1. **Go 后端检查 `access_token` 是否即将过期**（例如剩余有效期 <5 分钟）。
2. **如果快过期，在请求头中附加 `refresh_token`**（正常请求不携带）。
3. **Worker 检测到 `refresh_token` 存在时自动刷新**，返回新 `access_token`。
4. **如果 `refresh_token` 失效，Worker 返回 `401`，前端直接跳转登录页**。

```mermaid
sequenceDiagram
    participant Client as Wails前端
    participant Go as Go后端
    participant Worker as Cloudflare Worker
    participant API as 业务API

    Client->>Go: 请求数据 (仅 access_token)
    Go->>Go: 检查 access_token 是否即将过期
    alt 快过期
        Go->>Worker: 请求 + refresh_token (Header)
        Worker->>Supabase: 刷新 Token
        Supabase-->>Worker: 新 access_token
        Worker->>API: 转发请求 + 新 Token
        API-->>Worker: 数据
        Worker->>Go: 数据 + 新 access_token (Header)
        Go->>Client: 数据 + 更新本地 Token
    else 未过期
        Go->>Worker: 请求 (仅 access_token)
        Worker->>API: 转发请求
        API-->>Worker: 数据
        Worker->>Go: 数据
        Go->>Client: 数据
    end
```

## browser and driver

https://cdn.playwright.dev/dbazure/download/playwright/builds/chromium/1169/chromium-win64.zip
https://playwright.azureedge.net/builds/driver/playwright-1.52.0-win32_x64.zip
