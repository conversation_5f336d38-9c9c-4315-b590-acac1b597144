# .gitea/workflows/build.yml
name: Wails Cross-Platform Build

on:
  push:
    tags:
    # Match any new tag
      - '*'

env:
  # Necessary for most environments as build failure can occur due to OOM issues
  NODE_OPTIONS: "--max-old-space-size=4096"

jobs:
  build:
    strategy:
      matrix:
        platform:
          - os: windows
            runner: windows-latest
            target: windows/amd64
            artifact_name: ShanZhu-windows-amd64.exe
          - os: macos
            runner: macos-latest
            target: darwin/arm64
            artifact_name: ShanZhu-macos-arm64.app

    runs-on: ${{ matrix.platform.runner }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Build wails
        uses: https://github.com/dAppServer/wails-build-action@v2.2
        id: build
        with:
          build-name: ${{ matrix.platform.artifact_name }}
          build-platform: ${{ matrix.platform.target }}
          package: false
          go-version: '1.24.5'

      - name: Upload Artifact
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.platform.artifact_name }}
          path: build/bin/${{ matrix.platform.artifact_name }}
