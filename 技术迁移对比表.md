# 山竹阅卷系统技术迁移对比表

## 前端技术栈迁移对比

### 框架和库对比

| 功能类别 | 当前技术 | 新技术 | 迁移复杂度 | 备注 |
|---------|---------|--------|-----------|------|
| **前端框架** | React 18 | Vue 3 | 🔴 高 | 需要重写所有组件 |
| **UI组件库** | Ant Design | Element Plus | 🟡 中 | API相似，需要适配 |
| **状态管理** | React Context | Pinia | 🟡 中 | 概念相似，语法不同 |
| **路由管理** | React Router | Vue Router | 🟢 低 | 配置方式相似 |
| **构建工具** | Vite | Vite | 🟢 低 | 保持不变 |
| **类型系统** | TypeScript | TypeScript | 🟢 低 | 保持不变 |

### 组件迁移对比

| 组件类型 | React (Ant Design) | Vue 3 (Element Plus) | 迁移要点 |
|---------|-------------------|---------------------|---------|
| **按钮** | `<Button>` | `<el-button>` | 属性名基本一致 |
| **输入框** | `<Input>` | `<el-input>` | v-model 替代 value/onChange |
| **表单** | `<Form>` | `<el-form>` | 验证规则语法不同 |
| **对话框** | `<Modal>` | `<el-dialog>` | 显示控制方式不同 |
| **表格** | `<Table>` | `<el-table>` | 列定义方式不同 |
| **布局** | `<Layout>` | `<el-container>` | 结构相似 |
| **消息提示** | `message.info()` | `ElMessage()` | API调用方式相似 |

### 状态管理迁移

| 概念 | React Context | Pinia | 示例 |
|------|---------------|-------|------|
| **状态定义** | `createContext()` | `defineStore()` | 语法完全不同 |
| **状态访问** | `useContext()` | `useStore()` | 使用方式相似 |
| **状态更新** | `setState()` | `store.action()` | 更新机制不同 |
| **计算属性** | `useMemo()` | `computed()` | 概念相似 |

## 后端技术栈迁移对比

### 浏览器自动化对比

| 功能 | Playwright | ChromeDP | 迁移复杂度 | 备注 |
|------|-----------|----------|-----------|------|
| **浏览器启动** | `playwright.Run()` | `chromedp.NewContext()` | 🟡 中 | 启动方式不同 |
| **页面导航** | `page.Goto()` | `chromedp.Navigate()` | 🟢 低 | 概念相同 |
| **元素定位** | `page.Locator()` | `chromedp.WaitVisible()` | 🟡 中 | 定位方式不同 |
| **元素点击** | `locator.Click()` | `chromedp.Click()` | 🟢 低 | 功能相同 |
| **元素填充** | `locator.Fill()` | `chromedp.SendKeys()` | 🟢 低 | 功能相同 |
| **截图功能** | `locator.Screenshot()` | `chromedp.Screenshot()` | 🟡 中 | 参数格式不同 |
| **等待元素** | `locator.WaitFor()` | `chromedp.WaitVisible()` | 🟢 低 | 概念相同 |

### API 迁移对比

#### 浏览器管理

**Playwright 版本:**
```go
// 启动浏览器
pw, err := playwright.Run()
browserContext, err = pw.Chromium.LaunchPersistentContext(userDataDir, options)

// 获取页面
pages := browserContext.Pages()
page := pages[0]

// 导航
page.Goto(url)
```

**ChromeDP 版本:**
```go
// 启动浏览器
allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
taskCtx, _ := chromedp.NewContext(allocCtx)

// 导航
chromedp.Run(taskCtx, chromedp.Navigate(url))
```

#### 元素操作

**Playwright 版本:**
```go
// 点击元素
locator := page.Locator(selector).First()
locator.Click()

// 填充元素
locator.Fill(value)

// 截图
screenshotBytes, err := locator.Screenshot()
```

**ChromeDP 版本:**
```go
// 点击元素
chromedp.Run(taskCtx,
    chromedp.WaitVisible(selector),
    chromedp.Click(selector),
)

// 填充元素
chromedp.Run(taskCtx,
    chromedp.Clear(selector),
    chromedp.SendKeys(selector, value),
)

// 截图
var buf []byte
chromedp.Run(taskCtx,
    chromedp.Screenshot(selector, &buf, chromedp.NodeVisible),
)
```

## 文件结构迁移对比

### 前端文件结构

**当前结构 (React):**
```
frontend/src/
├── components/
│   ├── AboutDialog.tsx
│   ├── LoadingSpinner.tsx
│   └── ...
├── pages/
│   ├── Home.tsx
│   └── Login.tsx
├── contexts/
│   ├── AuthContext.tsx
│   └── LoadingContext.tsx
├── utils/
└── App.tsx
```

**新结构 (Vue 3):**
```
frontend/src/
├── components/
│   ├── common/          # 通用组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── views/               # 页面视图
│   ├── login/
│   ├── home/
│   └── grading/
├── stores/              # Pinia 状态
├── composables/         # 组合式函数
├── types/               # 类型定义
├── utils/
└── App.vue
```

### 后端文件结构

**当前结构:**
```
.
├── main.go
├── app.go
├── page.go
├── element_actions.go
├── database.go
├── config.go
└── ...
```

**新结构 (模块化):**
```
.
├── main.go
├── internal/
│   ├── app/             # 应用核心
│   ├── browser/         # 浏览器自动化
│   ├── grading/         # 阅卷业务
│   ├── config/          # 配置管理
│   ├── database/        # 数据库
│   └── logger/          # 日志
└── pkg/
    ├── types/           # 类型定义
    ├── utils/           # 工具函数
    └── errors/          # 错误定义
```

## 迁移风险评估

### 高风险项目 🔴

1. **前端组件重写**
   - 风险：所有 React 组件需要重写为 Vue 3
   - 缓解：逐个组件迁移，保持功能对等

2. **浏览器自动化行为差异**
   - 风险：ChromeDP 与 Playwright 可能存在行为差异
   - 缓解：充分测试，建立兼容层

3. **状态管理重构**
   - 风险：React Context 到 Pinia 的迁移可能引入 bug
   - 缓解：保持状态结构一致，逐步迁移

### 中风险项目 🟡

1. **UI 组件适配**
   - 风险：Ant Design 到 Element Plus 的 API 差异
   - 缓解：建立组件映射表，统一适配

2. **TypeScript 类型调整**
   - 风险：Vue 3 的类型系统与 React 不同
   - 缓解：重新定义类型，确保类型安全

### 低风险项目 🟢

1. **构建工具配置**
   - 风险：Vite 配置调整
   - 缓解：配置相对简单，风险可控

2. **后端业务逻辑**
   - 风险：Go 后端逻辑基本不变
   - 缓解：只需要重构代码结构

## 迁移时间估算

| 阶段 | 预计工作量 | 关键路径 |
|------|-----------|---------|
| **环境准备** | 3-5 天 | 依赖更新、项目结构 |
| **核心功能迁移** | 10-15 天 | 浏览器自动化、前端页面 |
| **业务功能完善** | 10-15 天 | 阅卷流程、配置管理 |
| **测试和优化** | 5-10 天 | 功能测试、性能优化 |

**总计：28-45 天 (约 6-9 周)**

## 成功指标

### 功能完整性
- [ ] 所有现有功能正常工作
- [ ] 用户界面美观易用
- [ ] 性能不低于原系统

### 代码质量
- [ ] 模块化程度提升
- [ ] 代码可维护性增强
- [ ] 类型安全得到保障

### 技术债务
- [ ] 消除技术债务
- [ ] 提升开发效率
- [ ] 便于后续扩展
