{"$schema": "https://wails.io/schemas/config.v2.json", "name": "山竹阅卷", "outputfilename": "山竹阅卷", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "obfuscated": false, "garbleargs": "", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "info": {"productName": "山竹阅卷", "productVersion": "v0.1.0", "companyName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyright": "Copyright © 2025 ShanZhuLab", "comments": "山竹阅卷"}}